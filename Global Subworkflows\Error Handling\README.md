# Error Handling Workflows

This folder contains standardized error handling workflows that provide centralized error management, notification, and recovery mechanisms for all N8N workflows in the repository.

## 🎯 Overview

Error handling workflows are designed to:
- **Centralize Error Management**: Single point for handling all workflow errors
- **Provide Consistent Notifications**: Standardized error reporting across all workflows
- **Enable Error Recovery**: Automated retry mechanisms and fallback procedures
- **Maintain System Reliability**: Prevent workflow failures from cascading

## 📋 Available Error Handling Workflows

### Global_Error_Notifier.json
**Purpose**: Centralized error notification system that sends email alerts when any workflow encounters an error.

**Execution Steps**:
1. <PERSON>rror Trigger detects workflow failures
2. Extracts error details (workflow name, error message, stack trace)
3. Formats error information into readable email
4. Sends notification via Gmail to designated recipient

**Use Case**: Immediate notification of workflow failures for rapid response and debugging.

## 🛠️ How to Use This Workflow

### 🚀 Quick Setup Guide

#### Step 1: Import the Error Workflow

1. **Open N8N Interface**
   - Navigate to your N8N instance
   - Go to **Workflows** section

2. **Import the Workflow**
   - Click **"+ Add workflow"** → **"Import from file"**
   - Select `Global_Error_Notifier.json` from this folder
   - Click **"Import"**
   - The workflow will appear as "Error Workflow"

#### Step 2: Configure Gmail Credentials

1. **Create Gmail Credential**
   - Go to **Settings** → **Credentials** in N8N
   - Click **"Add credential"**
   - Search for and select **"Gmail OAuth2 API"**
   - Click **"Continue"**
   - Follow the OAuth2 authorization process:
     - Sign in to your Google account
     - Grant N8N permission to send emails on your behalf
   - Save the credential with a descriptive name

2. **Link Credential to Workflow**
   - Open the imported "Error Workflow"
   - Click on the **Gmail** node
   - In the **Credential for Gmail** dropdown, select your newly created credential
   - Click **"Save"** to save the workflow

#### Step 3: Customize Email Settings

1. **Update Recipient Email**
   - In the Gmail node, find the **"To"** field
   - Replace `<EMAIL>` with your desired email address
   - You can add multiple recipients separated by commas

2. **Customize Email Content** (Optional)
   - **Subject**: Change from "error" to something more descriptive
   - **Message**: Modify the template to include additional information
   - Current template includes workflow name, error message, and stack trace

#### Step 4: Test the Setup

1. **Create a Test Workflow**
   - Create a new workflow with a Manual Trigger
   - Add a Code node with: `throw new Error('Test error notification');`
   - Save and execute this test workflow

2. **Verify Error Notification**
   - The test workflow should fail
   - Check your email for the error notification
   - Verify all error details are included correctly

#### Step 5: Activate the Error Workflow

1. **Enable the Workflow**
   - In the "Error Workflow", toggle the status to **"Active"**
   - The workflow will now automatically catch errors from all other workflows
   - **Important**: Only activate after successful testing

### Integration with Other Workflows

Error handling workflows automatically catch errors from any active workflow in your N8N instance. No additional configuration is needed in individual workflows - the Error Trigger node listens for all workflow failures globally.

## 📊 Workflow Outputs

### Global_Error_Notifier Output
```
Email Notification Sent: true
Recipient: <EMAIL>
Subject: error
Workflow Name: [Failed Workflow Name]
Error Message: [Detailed Error Description]
Stack Trace: [Technical Error Details]
Timestamp: [Error Occurrence Time]
```

## 🔧 Configuration Parameters

### Global_Error_Notifier Parameters
- **Recipient Email**: `<EMAIL>` (configurable)
- **Email Subject**: `error` (customizable)
- **Message Template**: Includes workflow name, error message, and stack trace
- **Trigger Type**: Error Trigger (automatic activation on any workflow error)

## 📋 Dependencies

### Required Credentials
- **Gmail OAuth2**: For sending error notification emails
  - Scope: Send emails
  - Required permissions: Gmail API access

### External Services
- **Gmail API**: For email delivery
- **N8N Error Trigger**: Built-in N8N functionality

## 🔧 Advanced Configuration

### Custom Email Templates

You can customize the email message by modifying the Gmail node's message parameter:

**Current Template:**
```
There is an error from Workflow: [{{ $json.workflow.name }}]
error is : {{ $json.execution.error.message }}
stack:{{ $json.execution.error.stack }}
```

**Available Variables:**
- `{{ $json.workflow.name }}` - Name of the failed workflow
- `{{ $json.execution.error.message }}` - Error message
- `{{ $json.execution.error.stack }}` - Full stack trace
- `{{ $json.execution.startedAt }}` - Execution start time
- `{{ $json.execution.stoppedAt }}` - Execution stop time

### Multiple Recipients

**Option 1: Multiple Email Addresses**
- In the Gmail node "To" field, separate emails with commas:
- `<EMAIL>, <EMAIL>, <EMAIL>`

**Option 2: Multiple Gmail Nodes**
- Duplicate the Gmail node for different recipients
- Connect both nodes to the Error Trigger
- Configure each with different email addresses

### Error Filtering

To filter which errors trigger notifications, add an IF node between Error Trigger and Gmail:

**Example Conditions:**
```
Only notify for production workflows:
{{ $json.workflow.name.includes('PROD_') }}

Exclude timeout errors:
{{ !$json.execution.error.message.includes('timeout') }}

Only critical errors:
{{ $json.execution.error.message.includes('CRITICAL') }}
```

## 🚨 Error Scenarios & Troubleshooting

### Common Issues

1. **No Error Emails Received**
   - Check if the error workflow is **Active** (toggle should be green)
   - Verify Gmail credentials are properly configured and authorized
   - Test Gmail credentials with a simple send email workflow
   - Check spam/junk folder in your email
   - Review N8N execution history for failed email attempts

2. **Gmail Authentication Errors**
   - Re-authorize the Gmail OAuth2 credential in Settings → Credentials
   - Ensure Gmail API is enabled in your Google Cloud Console
   - Check if 2-factor authentication is properly configured on your Google account
   - Verify the credential is selected in the Gmail node

3. **Missing or Incomplete Error Information**
   - Confirm the Error Trigger node is properly configured (no parameters needed)
   - Check that source workflows are generating proper error data
   - Review N8N execution logs for additional error context
   - Ensure the workflow causing errors is actually failing (not just warnings)

4. **Too Many Notifications**
   - Implement error filtering using IF nodes (see Advanced Configuration)
   - Consider rate limiting for repeated errors from the same workflow
   - Group similar errors to reduce notification volume
   - Add conditions to exclude non-critical errors

5. **Workflow Import Issues**
   - Ensure you're using the correct file: `Global_Error_Notifier.json`
   - Check file permissions and accessibility
   - Try downloading the file again if corrupted
   - Verify N8N version compatibility

### Testing Your Setup

1. **Create a Simple Test Workflow**
   ```
   Nodes needed:
   - Manual Trigger
   - Code node with: throw new Error('Test error for notification system');
   ```

2. **Execute the Test**
   - Run the test workflow manually
   - It should fail immediately
   - Check your email within 1-2 minutes for the notification

3. **Verify Email Content**
   - Subject should show "error" (or your custom subject)
   - Message should include:
     - Workflow name (your test workflow name)
     - Error message: "Test error for notification system"
     - Stack trace information

### Error Recovery
- If email delivery fails, check N8N execution history for the error workflow
- Manual retry is possible through the N8N interface
- Consider implementing backup notification methods (Slack, Discord, webhooks)
- Monitor Gmail API quotas to avoid rate limiting

## 📈 Changelog

### Version 1.0 (Current)
- Initial implementation with Gmail notification
- Basic error trigger and email formatting
- Single recipient email support

## 📝 To-Do & Known Issues

### Planned Enhancements
- [ ] Add support for multiple notification channels (Slack, Discord, SMS)
- [ ] Implement error severity levels and conditional notifications
- [ ] Add retry mechanisms for failed notifications
- [ ] Create error dashboard and reporting features
- [ ] Add error categorization and filtering

### Known Issues
- Single recipient limitation (only one email address supported)
- No error severity classification
- Limited error context information

## 🔄 Future Error Handling Workflows

### Planned Additions
- **Global_Retry_Handler**: Automated retry logic with exponential backoff
- **Global_Exception_Manager**: Advanced error categorization and routing
- **Global_Error_Dashboard**: Real-time error monitoring and analytics
- **Global_Recovery_Workflow**: Automated error recovery and fallback procedures

## 🤝 Contributing

### Adding New Error Handling Workflows
1. Follow the naming convention: `Global_Error_[Function]`
2. Include comprehensive error handling within the workflow itself
3. Provide detailed documentation following this template
4. Test with various error scenarios before deployment

### Modifying Existing Workflows
1. Test changes thoroughly to avoid breaking error handling
2. Update documentation to reflect changes
3. Consider backward compatibility with existing integrations
4. Notify team members of any breaking changes
