# Error Handling Workflows

This folder contains standardized error handling workflows that provide centralized error management, notification, and recovery mechanisms for all N8N workflows in the repository.

## 🎯 Overview

Error handling workflows are designed to:
- **Centralize Error Management**: Single point for handling all workflow errors
- **Provide Consistent Notifications**: Standardized error reporting across all workflows
- **Enable Error Recovery**: Automated retry mechanisms and fallback procedures
- **Maintain System Reliability**: Prevent workflow failures from cascading

## 📋 Available Error Handling Workflows

### Global_Error_Notifier.json
**Purpose**: Centralized error notification system that sends email alerts when any workflow encounters an error.

**Execution Steps**:
1. <PERSON>rror Trigger detects workflow failures
2. Extracts error details (workflow name, error message, stack trace)
3. Formats error information into readable email
4. Sends notification via Gmail to designated recipient

**Use Case**: Immediate notification of workflow failures for rapid response and debugging.

## 🛠️ How to Use Error Handling Workflows

### Setting Up Global Error Notification

1. **Import the Workflow**:
   - In N8N, go to Workflows → Import from File
   - Select `Global_Error_Notifier.json`
   - Click Import

2. **Configure Gmail Credentials**:
   - Go to Credentials in N8N
   - Create new Gmail OAuth2 credential
   - Authorize access to your Gmail account
   - Link the credential to the Gmail node in the workflow

3. **Customize Email Settings**:
   - Update the recipient email address in the Gmail node
   - Modify the email subject and message format if needed
   - Test the workflow to ensure notifications work

4. **Activate the Workflow**:
   - Set the workflow to "Active" status
   - This workflow will now catch errors from all other workflows

### Integration with Other Workflows

Error handling workflows automatically catch errors from any active workflow in your N8N instance. No additional configuration is needed in individual workflows.

## 📊 Workflow Outputs

### Global_Error_Notifier Output
```
Email Notification Sent: true
Recipient: <EMAIL>
Subject: error
Workflow Name: [Failed Workflow Name]
Error Message: [Detailed Error Description]
Stack Trace: [Technical Error Details]
Timestamp: [Error Occurrence Time]
```

## 🔧 Configuration Parameters

### Global_Error_Notifier Parameters
- **Recipient Email**: `<EMAIL>` (configurable)
- **Email Subject**: `error` (customizable)
- **Message Template**: Includes workflow name, error message, and stack trace
- **Trigger Type**: Error Trigger (automatic activation on any workflow error)

## 📋 Dependencies

### Required Credentials
- **Gmail OAuth2**: For sending error notification emails
  - Scope: Send emails
  - Required permissions: Gmail API access

### External Services
- **Gmail API**: For email delivery
- **N8N Error Trigger**: Built-in N8N functionality

## 🚨 Error Scenarios & Troubleshooting

### Common Issues

1. **Email Not Sending**:
   - Verify Gmail credentials are properly configured
   - Check Gmail API quotas and limits
   - Ensure the workflow is active

2. **Missing Error Details**:
   - Confirm Error Trigger is properly configured
   - Check that source workflows are generating proper error data

3. **Duplicate Notifications**:
   - Ensure only one instance of the error workflow is active
   - Check for multiple error handling workflows running simultaneously

### Error Recovery
- If email delivery fails, the error will be logged in N8N execution history
- Manual retry is possible through the N8N interface
- Consider implementing backup notification methods (Slack, Discord, etc.)

## 📈 Changelog

### Version 1.0 (Current)
- Initial implementation with Gmail notification
- Basic error trigger and email formatting
- Single recipient email support

## 📝 To-Do & Known Issues

### Planned Enhancements
- [ ] Add support for multiple notification channels (Slack, Discord, SMS)
- [ ] Implement error severity levels and conditional notifications
- [ ] Add retry mechanisms for failed notifications
- [ ] Create error dashboard and reporting features
- [ ] Add error categorization and filtering

### Known Issues
- Single recipient limitation (only one email address supported)
- No error severity classification
- Limited error context information

## 🔄 Future Error Handling Workflows

### Planned Additions
- **Global_Retry_Handler**: Automated retry logic with exponential backoff
- **Global_Exception_Manager**: Advanced error categorization and routing
- **Global_Error_Dashboard**: Real-time error monitoring and analytics
- **Global_Recovery_Workflow**: Automated error recovery and fallback procedures

## 🤝 Contributing

### Adding New Error Handling Workflows
1. Follow the naming convention: `Global_Error_[Function]`
2. Include comprehensive error handling within the workflow itself
3. Provide detailed documentation following this template
4. Test with various error scenarios before deployment

### Modifying Existing Workflows
1. Test changes thoroughly to avoid breaking error handling
2. Update documentation to reflect changes
3. Consider backward compatibility with existing integrations
4. Notify team members of any breaking changes
