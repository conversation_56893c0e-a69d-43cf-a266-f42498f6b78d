# Content Distribution Engine - Sub Workflows

Supporting workflows that handle specific tasks within the content distribution system. These subworkflows are designed to be modular, reusable, and maintainable components.

## 📋 Overview

The Content Distribution Engine uses two primary subworkflows:

1. **Notion Extract Content** - Retrieves and processes content from Notion database
2. **Update Notion Status** - Updates post status and tracking information after publishing

## 🔄 Subworkflow Details

### 1. Notion Extract Content (`Notion_Extract_Content.json`)

#### Purpose
Extracts ready-to-publish content from a Notion database, processes the content blocks, and formats them into markdown for distribution.

#### Trigger
- **Type**: Execute Workflow (called by main workflow)
- **Input**: None (uses manual trigger for testing)

#### Process Flow
1. **Content Retrieval**: Queries Notion database for posts with status "Ready"
2. **Block Extraction**: Retrieves all content blocks from each page
3. **Content Processing**: Converts Notion blocks to markdown format
4. **Data Aggregation**: Groups content by root page ID
5. **Output Formatting**: Structures data for main workflow consumption

#### Input Parameters
```json
{
  "databaseId": "200a7d56-8092-80b3-b40d-f244edf3b3a0",
  "filterStatus": "Ready"
}
```

#### Output Structure
When viewing the workflow execution results in N8N, the output data will appear as:

```
id: page-id
name: Post Title
author: <EMAIL>
markdownContent: # Title

Content...
property_cover_image: https://image-url.com
links: https://link1.com
images: https://image1.com
notionId: notion-page-id
notionUrl: https://notion.so/page-url
```

#### Key Nodes
- **Notion Database Query**: Filters posts by status
- **Block Retrieval**: Fetches all page content blocks
- **Markdown Conversion**: Converts blocks to markdown
- **Content Grouping**: Aggregates content by page
- **Data Formatting**: Structures output data

#### Configuration
```json
{
  "notion": {
    "databaseId": "your-database-id",
    "credentials": "Notion account 2",
    "filters": {
      "Status": "Ready"
    }
  }
}
```

### 2. Update Notion Status (`Update_notion_status_and_posted_workflow.json`)

#### Purpose
Updates the Notion database after successful content publishing, tracking which platforms the content was posted to and setting the overall status to "Published".

#### Trigger
- **Type**: Execute Workflow (called by main workflow)
- **Input**: Publishing results from main workflow

#### Process Flow
1. **Data Reception**: Receives publishing results from main workflow
2. **Notion Page Retrieval**: Gets current page data from Notion
3. **Platform Tracking**: Updates the platformPosted multi-select field
4. **Status Update**: Sets the post status to "Published"
5. **Data Merge**: Combines input data with Notion data

#### Input Parameters
```json
{
  "notionUrl": "https://notion.so/page-url",
  "platform": "LinkedIn",
  "id": "platform-post-id",
  "name": "Post Title",
  "author": "<EMAIL>",
  "markdownContent": "Content...",
  "property_cover_image": "https://image-url.com",
  "links": [],
  "images": [],
  "notionId": "notion-page-id"
}
```

#### Output Structure
When viewing the workflow execution results in N8N, the output data will appear as:

```
success: true
notionId: notion-page-id
status: published
platformsPosted: LinkedIn, Facebook
updatedAt: 2024-01-01T15:00:00Z
```

#### Key Nodes
- **Notion Page Get**: Retrieves current page data
- **Field Editor**: Processes platform tracking data
- **Notion Page Update**: Updates status and platform fields
- **Data Merge**: Combines workflow data

#### Configuration
```json
{
  "notion": {
    "credentials": "Notion account 2",
    "updateFields": {
      "Status": "published",
      "platformPosted": "dynamic_array"
    }
  }
}
```

## 🛠️ Technical Implementation

### Notion Extract Content - Detailed Flow

#### 1. Database Query Node
```json
{
  "resource": "databasePage",
  "operation": "getAll",
  "databaseId": "200a7d56-8092-80b3-b40d-f244edf3b3a0",
  "filters": {
    "conditions": [
      {
        "key": "Status|status",
        "condition": "equals",
        "statusValue": "Ready"
      }
    ]
  }
}
```

#### 2. Block Retrieval Node
```json
{
  "resource": "block",
  "operation": "getAll",
  "blockId": "={{ $json.url }}",
  "returnAll": true,
  "fetchNestedBlocks": true
}
```

#### 3. Markdown Conversion Code
```javascript
// Convert Notion blocks to markdown
const items = $input.all().map((item) => item.json);
let groupedItems = [];

items.forEach((item) => {
  let index = groupedItems.findIndex(obj => obj.rootId === item.rootId);
  if (index == -1) {
    groupedItems.push({rootId: item.rootId, markdownContent: ""});
  }
  index = groupedItems.findIndex(obj => obj.rootId === item.rootId);
  groupedItems[index].markdownContent += (item.markdownData + ' \n ');
});

return groupedItems;
```

### Update Notion Status - Detailed Flow

#### 1. Page Retrieval Node
```json
{
  "resource": "databasePage",
  "operation": "get",
  "pageId": "={{ $json.notionUrl }}"
}
```

#### 2. Platform Update Logic
```javascript
// Update platform posted array
const existing = $json.property_publish_to || [];
const additional = [$json.platform];
return Array.isArray(existing)
  ? [...new Set([...existing, ...additional])]
  : additional;
```

#### 3. Status Update Node
```json
{
  "resource": "databasePage",
  "operation": "update",
  "pageId": "={{ $json.notionUrl }}",
  "propertiesUi": {
    "propertyValues": [
      {
        "key": "platformPosted|multi_select",
        "multiSelectValue": "={{platform_array}}"
      },
      {
        "key": "Status|status",
        "statusValue": "published"
      }
    ]
  }
}
```

## 🔧 Configuration Guide

### Setting Up Notion Extract Content

1. **Database Configuration**
   - Ensure your Notion database has the required properties
   - Set up proper permissions for the integration
   - Configure the database ID in the workflow

2. **Credential Setup**
   - Create Notion integration in Notion workspace
   - Add integration to your database
   - Configure credentials in N8N

3. **Testing**
   - Create test content with status "Ready"
   - Execute the subworkflow manually
   - Verify markdown output format

### Setting Up Update Notion Status

1. **Input Validation**
   - Ensure input data includes all required fields
   - Validate notionUrl format
   - Check platform name consistency

2. **Field Mapping**
   - Map platformPosted multi-select options
   - Configure status field values
   - Set up proper data types

3. **Error Handling**
   - Implement retry logic for API failures
   - Add validation for missing data
   - Configure fallback procedures

## 🚨 Error Handling

### Common Issues

## 🔄 Maintenance

### Regular Tasks
1. **Credential Rotation**: Update Notion API credentials
2. **Performance Review**: Analyze execution metrics
3. **Content Validation**: Review processed content quality
4. **Error Analysis**: Investigate recurring issues

### Update Procedures
1. **Backup Workflows**: Export before making changes
2. **Test Changes**: Validate in development environment
3. **Gradual Deployment**: Roll out changes incrementally
4. **Monitor Results**: Watch for issues after updates

## 📚 Dependencies

### Required Credentials
- Notion API integration with database access
- Proper workspace permissions
- Valid authentication tokens

### N8N Node Requirements
- Notion node (v2.2+)
- Code node for data processing
- Set node for data manipulation
- Merge node for data combination

### External Dependencies
- Notion workspace access
- Database with proper schema
- Network connectivity to Notion API
