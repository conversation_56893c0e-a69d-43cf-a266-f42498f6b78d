# Content Distribution Engine

An intelligent, automated content distribution system that seamlessly publishes content from Notion to multiple social media platforms including LinkedIn, Facebook, Instagram, and Discord.

## 🎯 Overview

The Content Distribution Engine is a sophisticated N8N workflow system that automates the entire content publishing pipeline. It extracts content from a Notion database, formats it appropriately for each platform using AI, publishes to multiple social media channels, and updates tracking information back to Notion.

## 🏗️ Architecture

The system consists of three main components:

### 1. Main Workflow (`Content_Distribution_Engine.json`)
The orchestration layer that coordinates the entire publishing process:
- **Scheduled Execution**: Runs daily at 3 PM
- **Content Retrieval**: Calls the Notion Extract Content subworkflow
- **AI Processing**: Uses OpenAI to format content for each platform
- **Multi-Platform Publishing**: Distributes content to LinkedIn, Facebook, Instagram, and Discord
- **Status Updates**: Updates Notion with publishing status
- **Website Integration**: Publishes content to company website

### 2. Sub Workflows
Supporting workflows that handle specific tasks:
- **Notion Extract Content**: Retrieves ready-to-publish content from Notion
- **Update Notion Status**: Updates post status and platform tracking

### 3. Integration Points
External services and APIs:
- **Notion API**: Content source and status tracking
- **OpenAI API**: Content formatting and optimization
- **LinkedIn API**: Professional network publishing
- **Facebook Graph API**: Facebook and Instagram publishing
- **Discord Webhooks**: Team notifications
- **Company Website API**: Website content publishing

## 🚀 Features

### Intelligent Content Processing
- **AI-Powered Formatting**: Automatically adapts content for each platform's best practices
- **Image Optimization**: Handles image processing and optimization for different platforms
- **Link Management**: Extracts and formats links appropriately
- **Character Limits**: Respects platform-specific character limitations

### Multi-Platform Support
- **LinkedIn**: Professional posts with optional images and links
- **Facebook**: Rich media posts with image attachments
- **Instagram**: Visual content with captions and hashtags
- **Discord**: Team notifications and announcements

### Smart Scheduling
- **Automated Execution**: Daily scheduled runs at optimal posting times
- **Content Queue**: Processes multiple posts in sequence
- **Error Recovery**: Robust error handling and retry mechanisms
- **Status Tracking**: Real-time status updates in Notion

### Monitoring & Analytics
- **Execution Logging**: Detailed logs of all publishing activities
- **Error Reporting**: Comprehensive error tracking and notifications
- **Performance Metrics**: Publishing success rates and timing analytics
- **Platform Analytics**: Integration ready for social media metrics

## 🛠️ Setup Instructions

### Prerequisites
1. **N8N Instance**: Running N8N (v1.0+) with admin access
2. **Required N8N Nodes**: All necessary nodes installed (see list below)
3. **Notion Workspace**: Database configured with required properties
4. **Social Media Accounts**: Business accounts for LinkedIn, Facebook, Instagram
5. **API Access**: Valid API credentials for all integrated services

### Required N8N Nodes
Ensure these nodes are installed before importing workflows:
```
Core Nodes (usually pre-installed):
- Schedule Trigger
- Execute Workflow
- HTTP Request
- Switch
- Merge
- Code
- Set
- Split Out
- If

Community Nodes (may need installation):
- Notion
- OpenAI
- LinkedIn
```

### Step 1: Import Workflows (Critical Order)

**⚠️ Important**: Import workflows in this exact order to avoid dependency issues.

#### 1.1 Import Notion Extract Content Subworkflow
```
File: Sub Workflows/Notion_Extract_Conent.json
Purpose: Content extraction from Notion database
Dependencies: None (import first)
```

**Import Process:**
1. Open N8N interface
2. Click "New Workflow"
3. Click "..." menu → "Import from File"
4. Select `Notion_Extract_Conent.json`
5. Click "Import"
6. Save workflow as "Notion Extract Content"
7. **Note the Workflow ID** (you'll need this for the main workflow)

#### 1.2 Import Update Notion Status Subworkflow
```
File: Sub Workflows/Update_notion_status_and_posted_workflow.json
Purpose: Update post status after publishing
Dependencies: None (import second)
```

**Import Process:**
1. Create new workflow
2. Import `Update_notion_status_and_posted_workflow.json`
3. Save as "Update Notion Status and Posted Workflow"
4. **Note the Workflow ID**

#### 1.3 Import Main Workflow
```
File: Main Workflow/Content_Distribution_Engine.json
Purpose: Main orchestration workflow
Dependencies: Both subworkflows must be imported first
```

**Import Process:**
1. Create new workflow
2. Import `Content_Distribution_Engine.json`
3. Save as "Content Distribution Engine"
4. **Update Execute Workflow nodes** with correct subworkflow IDs

### Step 2: Post-Import Configuration

#### 2.1 Update Subworkflow References
In the main workflow, update Execute Workflow nodes:

1. **Find Execute Workflow Node** (calls Notion Extract Content)
   - Click on the "Execute Workflow" node
   - In the "Workflow" field, select "Notion Extract Content" from dropdown
   - Or manually enter the workflow ID noted during import

2. **Find Execute Workflow Node** (calls Update Notion Status)
   - Locate the second Execute Workflow node
   - Select "Update Notion Status and Posted Workflow"
   - Or enter the correct workflow ID

#### 2.2 Configure Credentials
Set up credentials for each service:

**Notion API:**
1. Go to https://www.notion.so/my-integrations
2. Create new integration
3. Copy the integration token
4. In N8N: Settings → Credentials → Add Credential → Notion API
5. Paste the token and save

**OpenAI API:**
1. Go to https://platform.openai.com/api-keys
2. Create new API key
3. In N8N: Add OpenAI credential
4. Paste API key and save

**LinkedIn OAuth2:**
1. Create LinkedIn app at https://www.linkedin.com/developers/
2. Get Client ID and Client Secret
3. In N8N: Add LinkedIn OAuth2 credential
4. Complete OAuth flow

**Facebook Graph API:**
1. Create Facebook app at https://developers.facebook.com/
2. Get access tokens for page and Instagram
3. In N8N: Add HTTP Request credentials or use directly in nodes

**Discord Webhook:**
1. Create webhook in Discord server settings
2. Copy webhook URL
3. Use directly in HTTP Request nodes

#### 2.3 Update Database and Platform IDs

**Notion Database ID:**
1. Open your Notion database
2. Copy database ID from URL (32-character string)
3. Update in Notion Extract Content workflow:
   - Find Notion node
   - Replace database ID: `200a7d56-8092-80b3-b40d-f244edf3b3a0`
   - With your database ID

**Platform IDs:**
Update these IDs in the main workflow:
```
Facebook Page ID: Replace "683603174832538"
Instagram Business ID: Replace "17841459370644146"
LinkedIn Person ID: Replace "k3ZTMMJ3Rg"
```

### Step 3: Verification and Testing

#### 3.1 Verify Import Success
For each workflow, check:
- [ ] All nodes are present and connected
- [ ] No red error indicators on nodes
- [ ] Workflow saves without errors
- [ ] Subworkflow references are correct

#### 3.2 Test Individual Components

**Test Notion Extract Content:**
1. Ensure you have content with status "Ready" in Notion
2. Execute the subworkflow manually
3. Verify it returns structured content data
4. Check for any error messages

**Test Update Notion Status:**
1. Execute with sample data
2. Verify it updates Notion page status
3. Check platform tracking functionality

**Test Main Workflow:**
1. Start with a single test post in Notion
2. Execute main workflow manually
3. Monitor each step for errors
4. Verify content is posted to platforms
5. Check Notion status updates

### Configuration Details

#### Notion Database Schema
Required properties in your Notion database:
```json
{
  "Status": {
    "type": "status",
    "options": ["Ready", "Published", "Draft"]
  },
  "platformPosted": {
    "type": "multi_select",
    "options": ["LinkedIn", "Facebook", "Instagram", "Discord"]
  },
  "Cover Image": {
    "type": "url"
  },
  "Content": {
    "type": "rich_text"
  },
  "Author": {
    "type": "email"
  },
  "Title": {
    "type": "title"
  }
}
```

#### Platform-Specific Settings
- **LinkedIn**: Person ID for posting to personal profile
- **Facebook**: Page ID and access token with publishing permissions
- **Instagram**: Business account ID linked to Facebook page
- **Discord**: Webhook URL for team notifications

## 📋 Usage

### Daily Operation
1. **Content Preparation**: Add content to Notion database with status "Ready"
2. **Automated Processing**: Workflow runs daily at 3 PM
3. **Multi-Platform Publishing**: Content is automatically distributed
4. **Status Updates**: Notion is updated with publishing results
5. **Team Notification**: Discord notification sent to team

### Manual Execution
1. Navigate to Content Distribution Engine workflow
2. Click "Execute Workflow" button
3. Monitor execution progress in N8N interface
4. Check Notion for status updates

### Content Guidelines
- **Title**: Clear, engaging headlines
- **Content**: Platform-appropriate content length
- **Images**: High-quality images with proper dimensions
- **Links**: Valid URLs for additional resources
- **Status**: Set to "Ready" when content is ready for publishing

## 🔧 Customization

### Adding New Platforms
1. Create new platform route in Switch node
2. Add platform-specific publishing logic
3. Update status tracking to include new platform
4. Test integration thoroughly

### Modifying Content Format
1. Update AI prompt in OpenAI node
2. Adjust content formatting logic
3. Test with various content types
4. Validate output across all platforms

### Scheduling Changes
1. Modify Schedule Trigger node settings
2. Consider optimal posting times for each platform
3. Test new schedule with sample content
4. Monitor performance metrics

## 🚨 Troubleshooting

### Common Issues
1. **Authentication Errors**: Check API credentials and permissions
2. **Content Formatting**: Verify AI prompt and content structure
3. **Platform Limits**: Ensure content meets platform requirements
4. **Network Issues**: Check API endpoint availability

### Error Handling
- Automatic retry mechanisms for transient failures
- Detailed error logging for debugging
- Fallback procedures for critical failures
- Manual intervention points for complex issues

### Monitoring
- Check N8N execution logs regularly
- Monitor Notion database for stuck posts
- Verify platform publishing success
- Review Discord notifications for issues

## 🔍 Troubleshooting Guide

### Quick Diagnostics
1. **Check Workflow Status**: Verify all workflows are active
2. **Validate Credentials**: Ensure all API credentials are valid
3. **Test Subworkflows**: Run subworkflows independently
4. **Monitor Logs**: Check N8N execution logs for errors

### Common Issues and Solutions

#### Notion Integration Issues
- **Database Not Found**: Verify database ID and integration permissions
- **No Content Retrieved**: Check if posts have status "Ready"
- **Permission Denied**: Ensure integration has database access
- **Content Format Errors**: Validate Notion block types and structure

#### Platform Publishing Failures
- **LinkedIn Errors**: Check OAuth2 credentials and person ID
- **Facebook/Instagram Errors**: Verify Graph API tokens and page permissions
- **Discord Errors**: Validate webhook URLs and channel permissions
- **Rate Limiting**: Implement delays between API calls

#### AI Processing Issues
- **OpenAI Errors**: Check API key and usage limits
- **Content Formatting**: Validate AI prompt and response format
- **Token Limits**: Ensure content fits within token limits
- **Response Parsing**: Verify JSON response structure

### Performance Issues
- **Slow Execution**: Check API response times and network connectivity
- **Memory Usage**: Monitor N8N instance resource usage
- **Timeout Errors**: Increase timeout settings for long-running operations
- **Concurrent Executions**: Limit simultaneous workflow runs

### Data Integrity Issues
- **Status Inconsistencies**: Verify status update logic
- **Platform Tracking**: Check multi-select field updates
- **Content Corruption**: Validate markdown conversion
- **Missing Images**: Verify image URL accessibility

## 📊 Monitoring and Analytics

### Key Metrics to Track
- **Execution Success Rate**: Percentage of successful workflow runs
- **Platform Publishing Success**: Success rate per platform
- **Content Processing Time**: Time from trigger to completion
- **Error Frequency**: Rate and types of errors encountered
- **API Response Times**: Performance of external API calls

### Monitoring Setup
1. **N8N Monitoring**: Enable detailed logging and monitoring
2. **External Monitoring**: Set up external health checks
3. **Alert Configuration**: Configure alerts for critical failures
4. **Performance Dashboards**: Create dashboards for key metrics

## 📚 Related Documentation

- [Main Workflow Details](./Main%20Workflow/README.md)
- [Subworkflows Documentation](./Sub%20Workflows/README.md)
- [Notion Extract Content](./Sub%20Workflows/Notion_Extract_Content_README.md)
- [Update Notion Status](./Sub%20Workflows/Update_Notion_Status_README.md)
- [Global Subworkflows](../Global%20Subworkflows/README.md)
