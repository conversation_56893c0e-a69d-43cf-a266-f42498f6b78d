# Content Distribution Engine - Main Workflow

The core orchestration workflow that coordinates the entire content distribution process from Notion to multiple social media platforms.

## 📋 Workflow Overview

**File**: `Content_Distribution_Engine.json`  
**Type**: Main Workflow  
**Trigger**: Schedule Trigger (Daily at 3 PM)  
**Purpose**: Orchestrate multi-platform content publishing from Notion

## 🔄 Workflow Flow

### 1. Initialization
- **Schedule Trigger**: Executes daily at 15:00 (3 PM)
- **Execute Workflow**: Calls the Notion Extract Content subworkflow
- **Content Retrieval**: Fetches ready-to-publish content from Notion database

### 2. Content Processing
- **AI Content Formatting**: Uses OpenAI to format content for each platform
- **Text Extraction**: Processes AI response to extract formatted posts
- **Content Splitting**: Separates content for individual platform processing

### 3. Platform Routing
- **Switch Node**: Routes content based on target platform
  - Facebook Route
  - LinkedIn Route  
  - Instagram Route
  - Discord Route

### 4. Platform-Specific Publishing

#### LinkedIn Publishing
- **Image Check**: Determines if post includes images
- **With Images**: Downloads image and creates LinkedIn post with media
- **Without Images**: Creates text-only LinkedIn post
- **Merge Results**: Combines outcomes for status tracking

#### Facebook Publishing
- **Image Upload**: Uploads image to Facebook if present
- **Post Creation**: Creates Facebook post with or without media
- **Result Tracking**: Captures post ID and status

#### Instagram Publishing
- **Media Creation**: Creates Instagram media container
- **Media Publishing**: Publishes the created media
- **Status Capture**: Records publishing results

#### Discord Publishing
- **Notification**: Sends team notification about published content
- **Channel Posting**: Posts announcement to designated Discord channel

### 5. Post-Publishing Operations
- **Status Updates**: Updates Notion database with publishing status
- **Website Integration**: Publishes content to company website
- **Result Merging**: Consolidates all platform results
- **Final Status**: Sets final status in Notion to "Published"

## 🛠️ Node Configuration

### Schedule Trigger
```json
{
  "rule": {
    "interval": [
      {
        "triggerAtHour": 15
      }
    ]
  }
}
```

### Execute Workflow (Notion Extract Content)
```json
{
  "workflowId": "pQQA474v1CG3uE3C",
  "workflowInputs": {
    "mappingMode": "defineBelow",
    "value": {}
  }
}
```

### OpenAI Content Formatting
```json
{
  "model": "gpt-4",
  "prompt": "Format the following content for social media platforms...",
  "temperature": 0.7,
  "maxTokens": 2000
}
```

### Platform Switch Logic
```json
{
  "rules": {
    "values": [
      {
        "conditions": {
          "conditions": [
            {
              "leftValue": "={{ $json.platform}}",
              "rightValue": "Facebook",
              "operator": "equals"
            }
          ]
        },
        "outputKey": "Facebook Route"
      }
    ]
  }
}
```

## 📊 Data Flow

### Input Data Structure
```json
{
  "id": "post-id",
  "name": "Post Title",
  "platform": "LinkedIn",
  "author": "<EMAIL>",
  "markdownContent": "Post content...",
  "property_cover_image": "https://image-url.com",
  "links": ["https://link1.com"],
  "images": ["https://image1.com"],
  "notionId": "notion-page-id",
  "notionUrl": "https://notion.so/page-url"
}
```

### Output Data Structure
When viewing the workflow execution results in N8N, the output data will appear as:

```
platform: LinkedIn
status: published
postId: platform-post-id
publishedAt: 2024-01-01T15:00:00Z
notionUpdated: true
websiteUpdated: true
```

## 📥 Import and Setup Instructions

### Prerequisites for Import
**⚠️ Critical**: Both subworkflows must be imported and configured BEFORE importing this main workflow.

Required subworkflows:
1. ✅ Notion Extract Content (imported first)
2. ✅ Update Notion Status (imported second)
3. 🔄 Content Distribution Engine (this workflow - import last)

### Step 1: Import Main Workflow

1. **Verify Subworkflows**: Ensure both subworkflows are imported and working
2. **Download File**: Ensure you have `Content_Distribution_Engine.json`
3. **Open N8N**: Navigate to your N8N instance
4. **Create New Workflow**: Click "New Workflow" or "+" button
5. **Import JSON**:
   - Click "..." menu (three dots) in top-right
   - Select "Import from File"
   - Choose `Content_Distribution_Engine.json`
   - Click "Import"
6. **Save Workflow**:
   - Click "Save" button
   - Name: "Content Distribution Engine"
   - Add tags: "content-distribution", "main-workflow", "automation"

### Step 2: Configure Subworkflow References

After import, you must update the Execute Workflow nodes to reference your imported subworkflows:

#### 2.1 Update Notion Extract Content Reference
1. **Find Execute Workflow Node**:
   - Look for the node labeled "Execute Workflow" (first one in the flow)
   - This should be connected after the Schedule Trigger

2. **Update Workflow Reference**:
   - Click on the Execute Workflow node
   - In the "Workflow" field dropdown, select "Notion Extract Content"
   - If not in dropdown, manually enter the workflow ID
   - Save the workflow

#### 2.2 Update Notion Status Reference
1. **Find Second Execute Workflow Node**:
   - Look for Execute Workflow nodes later in the flow
   - These are typically after the platform publishing nodes

2. **Update Workflow Reference**:
   - Click on each Execute Workflow node that calls status updates
   - Select "Update Notion Status and Posted Workflow"
   - Or enter the correct workflow ID manually

### Step 3: Configure All Credentials

#### 3.1 Notion API (if not already configured)
- Reuse credentials from subworkflows
- Or create new: Settings → Credentials → Notion API

#### 3.2 OpenAI API
1. **Get API Key**:
   - Go to https://platform.openai.com/api-keys
   - Create new API key
   - Copy the key

2. **Add to N8N**:
   - Settings → Credentials → Add Credential
   - Select "OpenAI"
   - Paste API key
   - Save as "OpenAi account"

#### 3.3 LinkedIn OAuth2
1. **Create LinkedIn App**:
   - Go to https://www.linkedin.com/developers/
   - Create new app
   - Get Client ID and Client Secret

2. **Configure OAuth2**:
   - Settings → Credentials → LinkedIn OAuth2 API
   - Enter Client ID and Secret
   - Complete OAuth flow
   - Save as "LinkedIn account"

#### 3.4 Facebook/Instagram Access Tokens
1. **Get Access Tokens**:
   - Create Facebook app at https://developers.facebook.com/
   - Get Page Access Token and Instagram Business Token
   - Note: These are used directly in HTTP Request nodes

2. **Update HTTP Request Nodes**:
   - Find Facebook and Instagram HTTP Request nodes
   - Update access tokens in the request parameters
   - Replace placeholder tokens with your actual tokens

#### 3.5 Discord Webhook
1. **Create Discord Webhook**:
   - Go to your Discord server settings
   - Create webhook for announcements channel
   - Copy webhook URL

2. **Update Discord Nodes**:
   - Find Discord HTTP Request nodes
   - Update webhook URLs
   - Test webhook functionality

### Step 4: Update Platform Configuration

#### 4.1 Replace Platform IDs
Update these IDs throughout the workflow:

**Facebook Page ID**:
- Find: `***************`
- Replace with: Your Facebook Page ID

**Instagram Business ID**:
- Find: `*****************`
- Replace with: Your Instagram Business Account ID

**LinkedIn Person ID**:
- Find: `k3ZTMMJ3Rg`
- Replace with: Your LinkedIn Person/Profile ID

#### 4.2 Update Access Tokens
Replace all access tokens with your actual tokens:

**Facebook Access Token**:
- Find: `EAAr5V5FvsnkBO6PrzVTCe5c3EIy2LSldoxFNJaXwQZCp97jvCaegAgTmdoGus7isZB6sch95wDwX5DAcCk45JTH2LFt47aOercUtU53GH53o5IZAOOCQR3VXHUOFN42gtRhxzHlePh1zgnTv6hGIMP9vTuIvzpIfRSf4vA9c4Rq9JCevpk2LWRpzl3yHUiedYEa`
- Replace with: Your Facebook Page Access Token

**Instagram Access Token**:
- Find: `EAAr5V5FvsnkBO4GItF26ExqPjK415InBPhv454ZBJ1wZAGcNGxwhr0MGmR2k0O92D304U93ydBoF3Uyebdad9r7Lz07qGZCG9tvZBiV6I31o7tBFoc76o6hFANrCTvBBlcEzaBHlJCsEtQeuEiE9qhnqYjrZA3PCvZBWbdCjS1RgeuDrmsaLSt0mZCOGZCC7Jw0V6VPF`
- Replace with: Your Instagram Business Access Token

### Step 5: Test Import and Configuration

#### 5.1 Verify Import Success
Check that:
- [ ] All nodes are visible and connected
- [ ] No red error indicators on nodes
- [ ] Subworkflow references are correct
- [ ] All credentials are configured
- [ ] Platform IDs are updated
- [ ] Access tokens are replaced

#### 5.2 Test Individual Components
1. **Test Subworkflow Calls**:
   - Execute just the Notion Extract Content call
   - Verify data flows correctly
   - Test status update calls

2. **Test Platform Publishing**:
   - Test each platform route individually
   - Verify API calls work with your credentials
   - Check content formatting

#### 5.3 End-to-End Test
1. **Prepare Test Content**:
   - Create test post in Notion with status "Ready"
   - Ensure it has title, content, and author

2. **Manual Execution**:
   - Execute the main workflow manually
   - Monitor each step for errors
   - Verify content posts to platforms
   - Check Notion status updates

3. **Verify Results**:
   - Check posts appear on social platforms
   - Verify Notion status changed to "Published"
   - Confirm platform tracking is updated

### Step 6: Configure Scheduling

#### 6.1 Set Schedule Trigger
1. **Update Schedule**:
   - Click on Schedule Trigger node
   - Set desired execution time (default: 3 PM daily)
   - Consider optimal posting times for your audience

2. **Activate Workflow**:
   - Toggle workflow to "Active" status
   - Monitor first scheduled execution
   - Verify automated operation

## 🔧 Configuration Parameters

### Required Credentials
- **Notion API**: Database access and page updates
- **OpenAI API**: Content formatting and optimization
- **LinkedIn OAuth2**: Personal profile posting
- **Facebook Graph API**: Page and Instagram posting
- **Discord Webhook**: Team notifications

### Environment Variables
```json
{
  "NOTION_DATABASE_ID": "200a7d56-8092-80b3-b40d-f244edf3b3a0",
  "FACEBOOK_PAGE_ID": "***************",
  "INSTAGRAM_BUSINESS_ID": "*****************",
  "LINKEDIN_PERSON_ID": "k3ZTMMJ3Rg"
}
```

### Platform-Specific Settings

#### LinkedIn Configuration
- **Person ID**: Target LinkedIn profile
- **Text Formatting**: Professional tone and formatting
- **Image Handling**: Automatic image download and attachment
- **Link Processing**: Automatic link expansion

#### Facebook Configuration
- **Page ID**: Target Facebook page
- **Access Token**: Page-level access token
- **Image Upload**: Automatic image processing
- **Post Formatting**: Facebook-optimized content

#### Instagram Configuration
- **Business Account ID**: Instagram business account
- **Media Creation**: Two-step media creation and publishing
- **Caption Formatting**: Instagram-optimized captions
- **Hashtag Processing**: Automatic hashtag inclusion

#### Discord Configuration
- **Webhook URL**: Team notification channel
- **Message Format**: Structured team notifications
- **Mention Handling**: Team member notifications

## 🚨 Error Handling

### Retry Mechanisms
- **HTTP Request Retries**: Automatic retry for failed API calls
- **Exponential Backoff**: Progressive delay between retries
- **Maximum Attempts**: Configurable retry limits

### Error Scenarios
1. **API Rate Limits**: Automatic backoff and retry
2. **Authentication Failures**: Credential validation and refresh
3. **Content Validation**: Input sanitization and validation
4. **Network Issues**: Timeout handling and retry logic


## 📚 Dependencies

### Required N8N Nodes
- Schedule Trigger
- Execute Workflow
- OpenAI
- HTTP Request
- LinkedIn
- Switch
- Merge
- Code
- Set
- Split Out
- If

### External Services
- Notion API
- OpenAI API
- LinkedIn API
- Facebook Graph API
- Instagram Basic Display API
- Discord Webhooks
- Company Website API

## 🔄 Maintenance

### Regular Tasks
- **Credential Renewal**: Monitor and renew API credentials
- **Performance Review**: Analyze execution metrics
- **Content Quality**: Review AI-generated content
- **Error Analysis**: Investigate and resolve recurring issues

### Update Procedures
1. **Backup Current Workflow**: Export before changes
2. **Test in Development**: Validate changes in test environment
3. **Gradual Rollout**: Deploy changes incrementally
4. **Monitor Results**: Watch for issues post-deployment
