# Error Handling Setup Guide

This guide provides step-by-step instructions for setting up the error handling workflows in your N8N instance.

## 🚀 Quick Setup

### Step 1: Import the Error Workflow

1. **Open N8N Interface**
   - Navigate to your N8N instance
   - Go to **Workflows** section

2. **Import the Workflow**
   - Click **"Import from File"** or **"+"** → **"Import from File"**
   - Select `Global_Error_Notifier.json` from this folder
   - Click **"Import"**

3. **Verify Import**
   - The workflow should appear as "Error Workflow" in your workflows list
   - You should see two nodes: "Error Trigger" and "Gmail"

### Step 2: Configure Gmail Credentials

1. **Create Gmail Credential**
   - Go to **Credentials** section in N8N
   - Click **"Create New"**
   - Select **"Gmail OAuth2 API"**
   - Follow the OAuth2 setup process:
     - Authorize N8N to access your Gmail account
     - Grant permission to send emails

2. **Link Credential to Workflow**
   - Open the imported error workflow
   - Click on the **Gmail** node
   - In the **Credential** dropdown, select your newly created Gmail credential
   - Save the workflow

### Step 3: Customize Email Settings

1. **Update Recipient Email**
   - In the Gmail node, change the **"Send To"** field
   - Replace `<EMAIL>` with your desired email address

2. **Customize Email Content** (Optional)
   - Modify the **Subject** field if desired
   - Update the **Message** template to include additional information
   - Current message includes:
     - Workflow name that failed
     - Error message
     - Stack trace

### Step 4: Activate the Workflow

1. **Enable the Workflow**
   - Toggle the workflow status to **"Active"**
   - The workflow will now automatically catch errors from all other workflows

2. **Test the Setup**
   - Create a simple test workflow that intentionally fails
   - Run the test workflow
   - Verify you receive an error notification email

## 🔧 Advanced Configuration

### Custom Email Templates

You can customize the email message by modifying the Gmail node's message parameter:

```
Current Template:
There is an error from Workflow: [{{ $json.workflow.name }}] 
error is : {{ $json.execution.error.message }}
stack:{{ $json.execution.error.stack }}

Available Variables:
- {{ $json.workflow.name }} - Name of the failed workflow
- {{ $json.execution.error.message }} - Error message
- {{ $json.execution.error.stack }} - Full stack trace
- {{ $json.execution.startedAt }} - Execution start time
- {{ $json.execution.stoppedAt }} - Execution stop time
```

### Multiple Recipients

To send notifications to multiple recipients:

1. **Option 1: Multiple Gmail Nodes**
   - Duplicate the Gmail node
   - Configure each with different recipient
   - Connect both to the Error Trigger

2. **Option 2: Mailing List**
   - Create a mailing list/group in Gmail
   - Use the group email as the recipient

### Error Filtering

To filter which errors trigger notifications:

1. **Add an IF Node**
   - Insert between Error Trigger and Gmail
   - Add conditions based on:
     - Workflow name
     - Error type
     - Error message content

2. **Example Conditions**
   ```
   Only notify for production workflows:
   {{ $json.workflow.name.includes('PROD_') }}
   
   Exclude specific error types:
   {{ !$json.execution.error.message.includes('timeout') }}
   ```

## 🔍 Troubleshooting

### Common Issues

**1. No Error Emails Received**
- Check if the error workflow is active
- Verify Gmail credentials are properly configured
- Test Gmail credentials with a simple send email workflow
- Check spam/junk folder

**2. Gmail Authentication Errors**
- Re-authorize the Gmail OAuth2 credential
- Ensure Gmail API is enabled in Google Cloud Console
- Check if 2-factor authentication is properly configured

**3. Incomplete Error Information**
- Verify the Error Trigger node is properly configured
- Check that source workflows are generating proper error data
- Review N8N execution logs for additional details

**4. Too Many Notifications**
- Implement error filtering using IF nodes
- Consider rate limiting for repeated errors
- Group similar errors to reduce notification volume

### Testing the Setup

1. **Create Test Workflow**
   ```json
   Simple test workflow that fails:
   - Manual Trigger
   - Function node with: throw new Error('Test error for notification');
   ```

2. **Run Test**
   - Execute the test workflow
   - Should receive error notification within minutes
   - Check email content for proper formatting

3. **Verify Error Details**
   - Workflow name should be correct
   - Error message should show "Test error for notification"
   - Stack trace should be included

## 📞 Support

If you encounter issues:

1. Check N8N execution logs for detailed error information
2. Verify all credentials are properly configured and authorized
3. Test individual components (Gmail sending, Error Trigger) separately
4. Review the Error Handling README.md for additional troubleshooting tips

## 🔄 Next Steps

After successful setup:

1. Monitor error notifications for the first few days
2. Adjust email templates based on your needs
3. Consider implementing additional error handling workflows
4. Plan for future enhancements like Slack notifications or error dashboards
