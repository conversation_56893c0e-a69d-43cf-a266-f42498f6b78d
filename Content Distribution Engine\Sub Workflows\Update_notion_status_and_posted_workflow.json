{"name": "Update notion status and posted workflow", "nodes": [{"parameters": {"resource": "databasePage", "operation": "update", "pageId": {"__rl": true, "value": "={{ $json.notionUrl }}", "mode": "url"}, "propertiesUi": {"propertyValues": [{"key": "platformPosted|multi_select", "multiSelectValue": "={{(() => {\n  const existing = $json.property_publish_to || [];\n  const additional = [ $json.platform];\n  return Array.isArray(existing)\n    ? [...new Set([...existing, ...additional])]\n    : additional;\n})()}}\n"}, {"key": "Status|status", "statusValue": "published"}]}, "options": {}}, "id": "dbcbf79e-d17e-4566-9c0a-9ea632c9aede", "name": "Set post status to \"Published\"", "type": "n8n-nodes-base.notion", "position": [440, 280], "typeVersion": 2.2, "credentials": {"notionApi": {"id": "2dGJhkNEGUxbFEcj", "name": "Notion account 2"}}}, {"parameters": {"resource": "databasePage", "operation": "get", "pageId": {"__rl": true, "value": "={{ $json.notionUrl }}", "mode": "url"}}, "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [100, -200], "id": "65e3ab28-22a9-4178-9449-07f8a0eea1d5", "name": "Notion", "credentials": {"notionApi": {"id": "2dGJhkNEGUxbFEcj", "name": "Notion account 2"}}}, {"parameters": {"assignments": {"assignments": [{"id": "b47f0173-679f-476d-996a-2e8d524f79f1", "name": "property_publish_to", "value": "={{ $json.property_platform_posted }}", "type": "array"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [400, -200], "id": "4948667f-fe4c-498e-836c-7e78175f5eb1", "name": "<PERSON>"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [700, -60], "id": "1fdfb1b4-5a78-445b-be35-312ae0e86cc4", "name": "<PERSON><PERSON>"}, {"parameters": {"inputSource": "passthrough"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-180, -40], "id": "f58f63b0-91c9-4a6c-b9a5-5e8e08cdd75a", "name": "When Executed by Another Workflow"}], "pinData": {"When Executed by Another Workflow": [{"json": {"id": "683603174832538_122101034936890691", "name": "new Blog", "platform": "Facebook", "author": "<EMAIL>", "markdownContent": "## The Rise of AI in Everyday Life \n Artificial Intelligence is transforming how we live and work. From smart assistants to recommendation systems, AI is making tasks faster and more efficient. Businesses use AI to automate operations and enhance customer experience. As technology advances, ethical use and transparency become more important. The future of AI holds limitless possibilities.", "property_cover_image": "", "links": [], "images": [], "notionId": "202a7d56-8092-800a-a0bd-ceb824fb8e5a", "notionUrl": "https://www.notion.so/new-Blog-202a7d568092800aa0bdceb824fb8e5a", "formattedPost": "The Rise of AI in Everyday Life\nArtificial Intelligence is rapidly changing our everyday routines, making tasks more efficient and our lives easier. From smart assistants that schedule our day to systems that improve our shopping experiences, AI is all around us. As we move forward, the importance of ethical use and transparency in these technologies cannot be overstated. What do you think about the limitless possibilities of AI? Let us know what you think!"}}]}, "connections": {"Set post status to \"Published\"": {"main": [[]]}, "Notion": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "Set post status to \"Published\"", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Notion", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "f6fb33d0-86b3-43ac-b926-71118df8b15a", "meta": {"templateCredsSetupCompleted": true, "instanceId": "36e066b6e7fd7141a4037cea4720adcd14b55b97aa9443f1eac8dd561c769073"}, "id": "2HqANHclXFYV7BFl", "tags": []}