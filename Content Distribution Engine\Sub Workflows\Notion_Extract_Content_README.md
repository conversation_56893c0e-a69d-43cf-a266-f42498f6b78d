# Notion Extract Content Subworkflow

**File**: `Notion_Extract_Content.json`  
**Type**: Subworkflow  
**Purpose**: Extract and process content from Notion database for content distribution

## 🎯 Overview

This subworkflow is responsible for retrieving ready-to-publish content from a Notion database, processing the content blocks, and converting them into a structured format suitable for multi-platform distribution.

## 📋 Functionality

### Core Features
- **Database Querying**: Filters Notion database for posts with "Ready" status
- **Content Extraction**: Retrieves all content blocks from selected pages
- **Markdown Conversion**: Converts Notion blocks to markdown format
- **Data Aggregation**: Groups content by page and structures output
- **Link Processing**: Extracts and formats embedded links
- **Image Handling**: Processes cover images and embedded media

### Input Requirements
- **Trigger**: Manual trigger or Execute Workflow call
- **Database Access**: Notion integration with read permissions
- **Content Status**: Posts must have status set to "Ready"

### Output Format
When viewing the workflow execution results in N8N, the output data will appear as:

```
id: unique-post-id
name: Post Title
author: <EMAIL>
markdownContent: # Title

Content in markdown format...
property_cover_image: https://cover-image-url.com
links: https://external-link1.com, https://external-link2.com
images: https://image1.com, https://image2.com
notionId: notion-page-id
notionUrl: https://notion.so/page-url
```

## 🔄 Workflow Steps

### 1. Manual Trigger
- **Node Type**: Manual Trigger
- **Purpose**: Allows manual execution for testing
- **Configuration**: Default settings

### 2. Notion Database Query
- **Node Type**: Notion
- **Operation**: Get All Database Pages
- **Configuration**:
  ```json
  {
    "databaseId": "200a7d56-8092-80b3-b40d-f244edf3b3a0",
    "returnAll": true,
    "filterType": "manual",
    "filters": {
      "conditions": [
        {
          "key": "Status|status",
          "condition": "equals",
          "statusValue": "Ready"
        }
      ]
    }
  }
  ```

### 3. Block Content Retrieval
- **Node Type**: Notion
- **Operation**: Get All Blocks
- **Configuration**:
  ```json
  {
    "blockId": "={{ $json.url }}",
    "returnAll": true,
    "fetchNestedBlocks": true
  }
  ```

### 4. Content Processing
- **Node Type**: Code
- **Purpose**: Convert blocks to markdown and group by page
- **Logic**:
  ```javascript
  const items = $input.all().map((item) => item.json);
  let groupedItems = [];
  
  items.forEach((item) => {
    let index = groupedItems.findIndex(obj => obj.rootId === item.rootId);
    if (index == -1) {
      groupedItems.push({rootId: item.rootId, markdownContent: ""});
    }
    index = groupedItems.findIndex(obj => obj.rootId === item.rootId);
    groupedItems[index].markdownContent += (item.markdownData + ' \n ');
  });
  
  return groupedItems;
  ```

### 5. Data Merging and Formatting
- **Node Type**: Merge
- **Purpose**: Combine page metadata with processed content
- **Configuration**: Combine by position

## 📥 Import and Setup Instructions

### Step 1: Import Workflow
**⚠️ Import Order**: This subworkflow must be imported **BEFORE** the main Content Distribution Engine workflow.

1. **Download File**: Ensure you have `Notion_Extract_Conent.json`
2. **Open N8N**: Navigate to your N8N instance
3. **Create New Workflow**: Click "New Workflow" or "+" button
4. **Import JSON**:
   - Click "..." menu (three dots) in top-right
   - Select "Import from File"
   - Choose `Notion_Extract_Conent.json`
   - Click "Import"
5. **Save Workflow**:
   - Click "Save" button
   - Name: "Notion Extract Content"
   - Add tags: "content-distribution", "notion", "subworkflow"
6. **Note Workflow ID**: Copy the workflow ID for main workflow configuration

### Step 2: Post-Import Configuration

#### 2.1 Configure Notion Credentials
1. **Create Notion Integration**:
   - Go to https://www.notion.so/my-integrations
   - Click "New integration"
   - Name: "N8N Content Distribution"
   - Select your workspace
   - Copy the integration token

2. **Add Credentials in N8N**:
   - Go to Settings → Credentials
   - Click "Add Credential"
   - Select "Notion API"
   - Paste integration token
   - Save as "Notion account 2" (or update node references)

3. **Share Database with Integration**:
   - Open your Notion database
   - Click "Share" button
   - Add your integration
   - Grant "Edit" permissions

#### 2.2 Update Database ID
1. **Get Database ID**:
   - Open your Notion database in browser
   - Copy the 32-character ID from URL
   - Example: `200a7d56-8092-80b3-b40d-f244edf3b3a0`

2. **Update Workflow**:
   - Find the Notion node in the workflow
   - Click on the node
   - In "Database" field, replace with your database ID
   - Save the workflow

#### 2.3 Verify Database Schema
Ensure your Notion database has these required properties:

| Property | Type | Options | Required |
|----------|------|---------|----------|
| Title | Title | - | Yes |
| Status | Status | Ready, Published, Draft | Yes |
| Author | Email | - | Yes |
| Cover Image | URL | - | No |
| Content | Rich Text | - | Yes |
| platformPosted | Multi-select | LinkedIn, Facebook, Instagram, Discord | No |

### Step 3: Test Import

#### 3.1 Verify Import Success
Check that:
- [ ] All nodes are visible and connected
- [ ] No red error indicators on nodes
- [ ] Notion node shows your database name
- [ ] Workflow saves without errors

#### 3.2 Test Execution
1. **Prepare Test Data**:
   - Create a test post in your Notion database
   - Set status to "Ready"
   - Add some content

2. **Manual Test**:
   - Click "Execute Workflow" button
   - Monitor execution progress
   - Check output data format
   - Verify content extraction works

#### 3.3 Common Import Issues

**Issue: "Node type 'notion' not found"**
- Solution: Install Notion node from N8N community packages
- Go to Settings → Community Packages → Install `@n8n/n8n-nodes-base.notion`

**Issue: "Database not found"**
- Solution: Check database ID and integration permissions
- Verify integration has access to the database
- Ensure database ID is correct (32 characters)

**Issue: "No content returned"**
- Solution: Check filter conditions
- Ensure posts have status "Ready"
- Verify database has content

### Step 4: Integration with Main Workflow

After successful import and testing:

1. **Note Workflow ID**: Copy this subworkflow's ID
2. **Import Main Workflow**: Follow main workflow import instructions
3. **Update References**: Ensure main workflow references this subworkflow correctly
4. **End-to-End Test**: Test the complete content distribution flow

## 🛠️ Configuration Details

### Database ID Configuration
Replace the database ID in the workflow:
```
Current: 200a7d56-8092-80b3-b40d-f244edf3b3a0
Replace with: your-database-id-here
```

## 🔧 Customization Options

### Content Filtering
Modify the database query to filter by different criteria:
```json
{
  "filters": {
    "conditions": [
      {
        "key": "Status|status",
        "condition": "equals",
        "statusValue": "Ready"
      },
      {
        "key": "Author|email",
        "condition": "equals",
        "emailValue": "<EMAIL>"
      }
    ],
    "combinator": "and"
  }
}
```

### Content Processing
Customize the markdown conversion logic:
```javascript
// Enhanced content processing
const processContent = (blocks) => {
  return blocks.map(block => {
    switch(block.type) {
      case 'heading_1':
        return `# ${block.heading_1.rich_text[0]?.plain_text || ''}`;
      case 'heading_2':
        return `## ${block.heading_2.rich_text[0]?.plain_text || ''}`;
      case 'paragraph':
        return block.paragraph.rich_text[0]?.plain_text || '';
      case 'bulleted_list_item':
        return `- ${block.bulleted_list_item.rich_text[0]?.plain_text || ''}`;
      default:
        return '';
    }
  }).join('\n\n');
};
```

### Output Formatting
Customize the output structure:
```javascript
// Custom output format
return {
  id: item.id,
  title: item.properties.Title?.title[0]?.plain_text,
  content: processedContent,
  metadata: {
    author: item.properties.Author?.email,
    createdAt: item.created_time,
    lastModified: item.last_edited_time
  },
  assets: {
    coverImage: item.properties['Cover Image']?.url,
    attachments: extractAttachments(item)
  }
};
```

## 🚨 Error Handling




## 🧪 Testing



### Validation Checklist
- [ ] Database connection successful
- [ ] Content filtering works correctly
- [ ] Markdown conversion accurate
- [ ] Output format matches specification
- [ ] Error handling functions properly
- [ ] Performance within acceptable limits

## 📚 Dependencies

### N8N Nodes Required
- Manual Trigger
- Notion (v2.2+)
- Code
- Merge

### External Dependencies
- Notion workspace access
- Database with proper schema
- Valid Notion integration token
- Network connectivity to Notion API

## 🔄 Maintenance

### Regular Maintenance Tasks
1. **Monitor Execution**: Check for failed executions
2. **Update Credentials**: Rotate Notion tokens as needed
3. **Review Content Quality**: Validate markdown output
4. **Performance Analysis**: Monitor execution times
5. **Error Pattern Analysis**: Identify recurring issues

### Update Procedures
1. **Backup Current Version**: Export workflow before changes
2. **Test in Development**: Validate changes with test data
3. **Gradual Rollout**: Deploy to production incrementally
4. **Monitor Post-Deployment**: Watch for issues after updates
