# Sahla Automations Repository

Welcome to the Sahla Smart Solutions - Automations Repository. This repository serves as the central version-controlled collection of all n8n automation workflows developed by the Automation team. Each workflow is self-contained and documented to ensure clarity, reproducibility, and maintainability.

## 📁 Repository Structure

The repository is organized to support clean versioning, collaboration, and reusability.

```
/workflows
│
├── [workflow-name-1]/
│   ├── workflow.json           # Exported JSON file of the n8n workflow
│   ├── README.md               # Documentation and changelog for the workflow
│   ├── any-supporting-files/   # Optional files required by the workflow
│
├── [workflow-name-2]/
│   ├── workflow.json
│   ├── README.md
│   └── ...
│
└── ...
Each subfolder represents a single, standalone n8n workflow.
```
```
sahla-n8n-workflows/
├── README.md                           # This file
├── Content Distribution Engine/        # Main content distribution system
│   ├── README.md                      # Detailed system documentation
│   ├── Main Workflow/                 # Primary orchestration workflow
│   │   ├── README.md                  # Main workflow documentation
│   │   └── Content_Distribution_Engine.json
│   └── Sub Workflows/                 # Supporting subworkflows
│       ├── README.md                  # Subworkflows documentation
│       ├── Notion_Extract_Content.json
│       └── Update_notion_status_and_posted_workflow.json
└── Global Subworkflows/               # Reusable components for future workflows
    └── README.md                      # Framework documentation
```



## 📚 Workflow Documentation Template

Each workflow folder must contain a README.md file structured as follows:

```
##1. 📝 Overview
A broad description of what the workflow is designed to do. Include the following:

Objective: The purpose of the workflow

Use Case: Where or how it’s used in the business

Execution Steps: Step-by-step explanation of how the workflow works (logic and actions taken)

## 2. 🔄 Changelog
  Document the evolution of the workflow. Include:

## Changelog

- v1.0 – [YYYY-MM-DD] – Initial release
- v1.1 – [YYYY-MM-DD] – Fixed trigger bug, added Slack integration
- v1.2 – [YYYY-MM-DD] – Refactored to support environment variables

## 3. 🧪 To-Do & Known Issues
Keep a living list of:

Outstanding tasks (new features, enhancements)

Reported bugs or malfunctions

Example:

## To-Do & Known Issues

- [ ] Add email fallback if Slack fails
- [ ] Refactor the HTTP node to use credentials
- [x] Fix incorrect date format in API response

## 4. 🎓 How to Use This Workflow
Explain the deployment and usage process:

Prerequisites (environment variables, credentials, services)

Step-by-step installation or import instructions

How to test or verify it is working

Expected outputs

Example:
## How to Use This Workflow

1. Import the `workflow.json` into n8n.
2. Configure the following credentials:
  - Slack API Key
  - Google Sheets Auth
3. Set the required environment variables:
  - `SLACK_CHANNEL`
  - `GOOGLE_SHEET_ID`
4. Activate the workflow.
5. Test by triggering an event manually via webhook or scheduled node.

Output: You should see a message posted in Slack and a row appended to the sheet.
✅ Contribution Guidelines
Every new workflow must follow the above structure.

All changes must be documented in the workflow’s README.md changelog.

Workflows should be tested and reviewed before pushing to main.

Keep your commit messages clear and descriptive.
```

# ## 🔁 Version Control & Workflow Management

To ensure proper collaboration and maintainability, please adhere to the following version control strategy and best practices:

### Branching Strategy

We follow a Git-based development workflow structured as follows:

- **`main`**: Stable, production-ready version of all workflows.

- **`develop`**: Ongoing integration of new features and updates before promotion to `main`.

- **Feature/Update Branches**:
  
  - Create a new branch from `develop` for each workflow or update using the naming convention:
    
    pgsql
    
    CopyEdit
    
    `feature/<workflow-name> update/<workflow-name> bugfix/<workflow-name>`
  
  - Examples:
    
    - `feature/slack-notification`
    
    - `update/google-sheets-logging`
    
    - `bugfix/incorrect-date-format`

- Submit **Pull Requests** (PRs) to merge into `develop` only after:
  
  - Testing the workflow
  
  - Updating the workflow’s `README.md` (changelog, todo, and documentation)

- Once a stable set of updates is validated, `develop` can be merged into `main` by a repository maintainer.

### Versioning Guidelines

- Use **semantic versioning** (`vX.Y.Z`) for each workflow's changelog:
  
  - **Major** (X): Breaking changes or complete rewrites
  
  - **Minor** (Y): New features, backward compatible
  
  - **Patch** (Z): Bug fixes or small enhancements

- Tag versions in `workflow.json` and reflect them in the respective folder’s `README.md`.

### Best Practices

- **One workflow per branch**: Avoid combining unrelated changes in a single PR.

- **Keep commits atomic**: Each commit should represent a single change or purpose.

- **Write meaningful commit messages**: e.g., `Fix: Add retry logic to webhook`, `Feat: Send daily summary via email`.

- **Test before pushing**: Ensure all nodes execute as expected and no credentials or secrets are hard-coded.

- **Document thoroughly**: Keep the `README.md` up to date with usage, known issues, and testing notes.

- **Avoid duplicate IDs**: If cloning workflows, regenerate node IDs before committing to avoid collisions



### Import Order (Important)
Import workflows in this exact order:

1. **Notion Extract Content** - `Content Distribution Engine/Sub Workflows/Notion_Extract_Conent.json`
2. **Update Notion Status** - `Content Distribution Engine/Sub Workflows/Update_notion_status_and_posted_workflow.json`
3. **Content Distribution Engine** - `Content Distribution Engine/Main Workflow/Content_Distribution_Engine.json`

### Import Steps

1. **Open N8N Interface**
   - Navigate to your N8N instance
   - Go to the Workflows section

2. **Create New Workflow**
   - Click "New Workflow" or "+" button

3. **Import JSON File**
   - Click the "..." menu (three dots) in the top-right corner
   - Select "Import from File"
   - Choose the JSON file from your computer
   - Click "Import"

4. **Save Workflow**
   - Click "Save" button
   - Give it a descriptive name
   - Save the workflow

5. **Repeat for All Files**
   - Follow the same process for each JSON file
   - Import in the order listed above

### After Import
- Configure your API credentials (Notion, OpenAI, LinkedIn, Facebook, Discord)
- Update database IDs and platform settings
- Test each workflow individually
- See individual README files for detailed setup instructions
