{"name": "Error Workflow", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.errorTrigger", "typeVersion": 1, "position": [-260, 40], "id": "8f671a46-8be3-46fb-aa8e-629fe69a638c", "name": "<PERSON><PERSON><PERSON>"}, {"parameters": {"sendTo": "<EMAIL>", "subject": "error", "message": "=There is an error from Workflow: [{{ $json.workflow.name }}] error is : {{ $json.execution.error.message }}\nstack:{{ $json.execution.error.stack }}", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-40, 40], "id": "fb69336b-9764-43a7-b122-63fd1bb9af5a", "name": "Gmail", "webhookId": "e3dc5f1e-d74d-43a6-b2a1-749b599e8e08"}], "pinData": {}, "connections": {"Error Trigger": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "02d69cdb-79a1-4a70-b8e6-97004279d88c", "meta": {"instanceId": "36e066b6e7fd7141a4037cea4720adcd14b55b97aa9443f1eac8dd561c769073"}, "id": "QjfQ9CWNToFgyvDh", "tags": []}