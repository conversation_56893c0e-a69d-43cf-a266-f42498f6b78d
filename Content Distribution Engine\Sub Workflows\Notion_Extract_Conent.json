{"name": "Notion Extract Conent", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-340, -40], "id": "50194e76-b3fc-4638-9f31-482dff90f04c", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"resource": "databasePage", "operation": "getAll", "databaseId": {"__rl": true, "value": "200a7d56-8092-80b3-b40d-f244edf3b3a0", "mode": "list", "cachedResultName": "Content Distibution", "cachedResultUrl": "https://www.notion.so/200a7d56809280b3b40df244edf3b3a0"}, "returnAll": true, "filterType": "manual", "filters": {"conditions": [{"key": "Status|status", "condition": "equals", "statusValue": "Ready"}]}, "options": {}}, "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [-80, -40], "id": "cb1ca30b-865a-4297-a3aa-472d0e8cc7c8", "name": "Notion", "credentials": {"notionApi": {"id": "2dGJhkNEGUxbFEcj", "name": "Notion account 2"}}}, {"parameters": {"resource": "block", "operation": "getAll", "blockId": {"__rl": true, "value": "={{ $json.url }}", "mode": "url"}, "returnAll": true, "fetchNestedBlocks": true}, "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [140, -40], "id": "c10ffc9f-3486-4327-87ae-5c82d1d9aa4c", "name": "Notion1", "credentials": {"notionApi": {"id": "2dGJhkNEGUxbFEcj", "name": "Notion account 2"}}}, {"parameters": {"assignments": {"assignments": [{"id": "fe463d87-caa4-4e83-b4bf-65f3a84534ce", "name": "parent_id", "value": "={{ $json.parent_id }}", "type": "string"}, {"id": "16dc51ca-70bb-4881-ac09-b5a0ae6b75ee", "name": "id", "value": "={{ $json.id }}", "type": "string"}, {"id": "c18c0cec-11be-43ea-af5f-1c6aad3ed4a1", "name": "type", "value": "={{ $json.type }}", "type": "string"}, {"id": "9fd181eb-7e8b-4ef0-ad2c-8a64736abb18", "name": "root_id", "value": "={{ $json.root_id }}", "type": "string"}, {"id": "9b0df4b3-2874-4eda-95ae-e57b79ecca8d", "name": "content", "value": "={{ $json.content }}", "type": "string"}, {"id": "bc7ee9b0-fcf7-4c69-a50e-43e2ec0f25de", "name": "table_row", "value": "={{ $json.table_row }}", "type": "string"}, {"id": "df7f6190-95ab-49da-b817-90baf34b6be9", "name": "bookMarkUrl", "value": "={{ $json.bookmark.url }}", "type": "string"}, {"id": "097d0648-a335-464b-b7e2-52c48a9b927d", "name": "imageFileUrl", "value": "={{ $json.image.file.url }}", "type": "string"}, {"id": "08135bf5-4584-4a0a-8289-97b2c54a9bc5", "name": "imageExternalUrl", "value": "={{ $json.image.external.url }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [420, -40], "id": "d5a1029c-abcd-4bc7-9abf-e330f84e590a", "name": "<PERSON>"}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nlet blocks = $input.all();\nlet tableData = [];\nfor (const block of blocks) {\n  if(block.json.type == \"table\")\n  {\n      \n  }\n  else if(block.json.type == \"table_row\")\n  {\n    \n  }\n  else if(block.json.type == \"heading_1\")\n  {\n    block.json.markdownContent = `#${block.json.content}#`;\n  }\n  else if (block.json.type == \"heading_2\")\n  {\n    block.json.markdownContent = `##${block.json.content}##`;\n  }\n  else if (block.json.type == \"heading_3\")\n  {\n    block.json.markdownContent = `###${block.json.content}###`;\n  }\n  else if (block.json.type == \"paragraph\")\n  {\n    block.json.markdownContent = `${block.json.content}\\n\\n`;\n  }\n  else if (block.json.type == \"bulleted_list_item\")\n  {\n    block.json.markdownContent = `-${block.json.content}\\n`;\n  }\n  else if (block.json.type == \"numbered_list_item\")\n  {\n    block.json.markdownContent = `1.${block.json.content}\\n`;\n  }\n  else if (block.json.type == \"quote\")\n  {\n    block.json.markdownContent = `>${block.json.content}\\n`;\n  }\n  else if (block.json.type == \"code\")\n  {\n    block.json.markdownContent = `\\`\\`\\`\\n${block.json.content}\\n\\`\\`\\`\\n\\n`;\n  }\n  else\n  {\n    block.json.markdownContent = `${block.json.content}\\n\\n`;\n  }\n  \n}\n\nreturn $input.all();"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [620, 200], "id": "3060e9b6-1c60-4a9b-a2f9-320aa03df323", "name": "Code"}, {"parameters": {"mode": "combineBySql", "query": "SELECT * FROM input1 LEFT JOIN \ninput2 ON input1.url LIKE CONCAT('%', input2.rootId, '%')"}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [1180, -100], "id": "11cdeb7b-302d-405d-a42a-f5ae866aefc2", "name": "<PERSON><PERSON>"}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nconst items = $input.all().map((item) => item.json);\nlet groupedItems = [];\nitems.forEach((item) => {\n  let index = groupedItems.findIndex(obj => obj.rootId === item.rootId);\n  if (index == -1) {\n    groupedItems.push({rootId:  item.rootId , markdownContent: \"\"});\n  }\n  index = groupedItems.findIndex(obj => obj.rootId === item.rootId);\n  groupedItems[index].markdownContent+= (item.markdownData + ' \\n ');\n});\n\nreturn groupedItems;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [960, -40], "id": "cea2faa4-2873-4c17-9254-eb2b6d12cc4d", "name": "Code1"}, {"parameters": {"jsCode": "const blocks = $input.all();\n\nfunction convertToMarkdown(data) {\n    let markdown = [];\n    let tableContent = [];\n    let newTable = true;\n    let tableRootId = null;\n    for (let i = 0; i < data.length; i++) {\n        if (data[i].json.type === 'table_row') {\n            if (newTable) {\n                tableContent = [];\n                newTable = false;\n                tableRootId = data[i].json.root_id;\n                let tableRow = JSON.parse(data[i].json.table_row).cells;\n                let row = [];\n                for (let j = 0; j < tableRow.length; j++) {\n                    for (let k = 0; k < tableRow[j].length; k++) {\n                        row.push(tableRow[j][k].plain_text);\n                    }\n                }\n                tableContent.push(row);\n            }\n            else {\n                if (data[i].json.root_id != tableRootId) {\n                    let contentMarkdown = convertTableDataToMarkdown(tableContent);\n                    markdown.push({ rootId: tableRootId, markdownData: contentMarkdown });\n                    tableContent = [];\n                    tableRootId = data[i].json.root_id;\n                }\n                let tableRow = JSON.parse(data[i].json.table_row).cells;\n                let row = [];\n                for (let j = 0; j < tableRow.length; j++) {\n                    for (let k = 0; k < tableRow[j].length; k++) {\n                        row.push(tableRow[j][k].plain_text);\n                    }\n                }\n                tableContent.push(row);\n            }\n        }\n        else {\n            if (!newTable && tableContent.length > 0) {\n                let contentMarkdown = convertTableDataToMarkdown(tableContent);\n                markdown.push({ rootId: tableRootId, markdownData: contentMarkdown });\n                tableContent = [];\n                tableRootId = null;\n                newTable = true;\n            }\n\n            if (data[i].json.type === 'heading_1') {\n                markdown.push({ rootId: data[i].json.root_id, markdownData: `# ${data[i].json.content}` });\n            }\n            else if (data[i].json.type === 'heading_2') {\n                markdown.push({ rootId: data[i].json.root_id, markdownData: `## ${data[i].json.content}` });\n            }\n            else if (data[i].json.type === 'heading_3') {\n                markdown.push({ rootId: data[i].json.root_id, markdownData: `### ${data[i].json.content}` });\n            }\n            else if (data[i].json.type === 'heading_4') {\n                markdown.push({ rootId: data[i].json.root_id, markdownData: `#### ${data[i].json.content}` });\n            }\n            else if (data[i].json.type === 'paragraph') {\n                if (data[i].json.content.trim() !== '') {\n                    markdown.push({ rootId: data[i].json.root_id, markdownData: data[i].json.content });\n                }\n            }\n            else if (data[i].json.type === 'bulleted_list_item') {\n                markdown.push({ rootId: data[i].json.root_id, markdownData: `- ${data[i].json.content}` });\n            }\n            else if (data[i].json.type === 'numbered_list_item') {\n                markdown.push({ rootId: data[i].json.root_id, markdownData: `1. ${data[i].json.content}` });\n            }\n            else if(data[i].json.type === 'bookmark')\n            {\n                markdown.push({ rootId: data[i].json.root_id, markdownData: `[Bookmark](${data[i].json.bookMarkUrl})` });\n            }\n            else if(data[i].json.type === 'image')\n            {\n                if(data[i].json.imageFileUrl != null)\n                {\n                  markdown.push({ rootId: data[i].json.root_id, markdownData: `![Alt text](${data[i].json.imageFileUrl})` });\n                }\n                else if(data[i].json.imageExternalUrl != null)\n                {\n                  markdown.push({ rootId: data[i].json.root_id, markdownData: `![Alt text](${data[i].json.imageExternalUrl})` });\n                }\n            }\n        }\n    }\n    return markdown;\n}\n\nfunction convertTableDataToMarkdown(tableData) {\n    console.log(\"tableData: \", tableData);\n    let markdown = '| ' + tableData[0].join(' | ') + ' |\\n';\n    markdown += '| ' + tableData[0].map(() => '---').join(' | ') + ' |\\n';\n    for (let i = 1; i < tableData.length; i++) {\n        markdown += '| ' + tableData[i].join(' | ') + ' |\\n';\n    }\n    return markdown;\n}\n\nreturn convertToMarkdown(blocks)\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, -40], "id": "f59b33d1-638d-49d9-a335-78655f1ddc6f", "name": "Code2"}], "pinData": {}, "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "Notion", "type": "main", "index": 0}]]}, "Notion": {"main": [[{"node": "Notion1", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Notion1": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Code2", "type": "main", "index": 0}]]}, "Code": {"main": [[]]}, "Code1": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Code2": {"main": [[{"node": "Code1", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "40a58b70-2a22-4f50-88f6-c108f8097c04", "meta": {"templateCredsSetupCompleted": true, "instanceId": "36e066b6e7fd7141a4037cea4720adcd14b55b97aa9443f1eac8dd561c769073"}, "id": "pQQA474v1CG3uE3C", "tags": []}