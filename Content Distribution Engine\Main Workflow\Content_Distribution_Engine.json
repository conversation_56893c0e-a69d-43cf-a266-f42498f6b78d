{"name": "Content Distribution Engine", "nodes": [{"parameters": {"rule": {"interval": [{"triggerAtHour": 15}]}}, "id": "021ce6ba-f295-4183-83f7-b4814bd4af16", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-1560, 220], "typeVersion": 1.2}, {"parameters": {"content": "## Execute Notion Workflow\n\nexecute workflow taht", "height": 642, "width": 630, "color": 6}, "id": "2de3de6f-da8b-41ff-9013-2de796dbba42", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1380, -120], "typeVersion": 1}, {"parameters": {"content": "## Format Post\nSend the post to OpenAI, where it will attempt to ask your assistant how to take the incoming blob of text, and soup it up into something more palpable for LinkedIn engagement.", "height": 541, "width": 721}, "id": "5e2bb674-3649-4e10-a20e-60c1eb0f1a53", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-580, -100], "typeVersion": 1}, {"parameters": {"content": "## 🔁 Route Post Based on Platform and Continue Unified Actions\n\nUse a **Switch Node** in n8n to check the `platform` field (e.g., \"LinkedIn\", \"Facebook\", or \"Instagram\") and route the post accordingly:\n\n### 🟦 If platform is `LinkedIn`:\n- Send combined **Content** to the **LinkedIn Node** to publish the post.\n\n### 🔵 If platform is `Facebook`:\n- Send the full **Content*** to the **Facebook Node** to publish the post.\n\n### 🟠 If platform is `Instagram`:\n- Send a **short caption + image** to the **Instagram Node** to publish the post.\n", "height": 540.7357881640437, "width": 860.9829802912225, "color": 6}, "id": "be0d1f24-3bc4-42e2-99ec-cc8c7148df36", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [380, -120], "typeVersion": 1}, {"parameters": {"authentication": "webhook", "content": "={{ $json.formattedPost }}", "options": {}}, "type": "n8n-nodes-base.discord", "typeVersion": 2, "position": [1440, 740], "id": "7139e499-0324-4d57-ba97-c9d014596cf6", "name": "Discord Announcement", "webhookId": "36d32ec8-6241-4291-99c5-80b9da6a901b", "retryOnFail": true, "credentials": {"discordWebhookApi": {"id": "aKcV7mI0PSB4c7Bp", "name": "Discord Webhook account"}}, "onError": "continueRegularOutput"}, {"parameters": {"content": "# Todo\n## n8n Automation Steps :\n\n### 1-Trigger: Detect new content from Notion or file uploads.\n\n### 2-Format: Customize content per platform using a Function node.\n\n### 3-Post: Distribute via Buffer API.\n\n### 4-Announce: Post internally in Discord.\n\n### 5-Publish: Push to website via CMS.\n\n### 6-Log: Record the event in Airtable or Notion.", "height": 542, "width": 630, "color": 2}, "id": "b6943ac2-d081-401d-aede-0cd3fe03ce79", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-2260, -80], "typeVersion": 1}, {"parameters": {"method": "POST", "url": "https://65e8-197-37-197-81.ngrok-free.app/cms/api/upload-markdown.php", "sendBody": true, "bodyParameters": {"parameters": [{"name": "title", "value": "={{ $json.name }}"}, {"name": "author", "value": "={{ $json.property_author }}"}, {"name": "category", "value": "={{ $json.output.category }}"}, {"name": "markdown_content", "value": "={{ $json.markdownContent }}"}, {"name": "tags", "value": "={{ $json.output.tags }}"}, {"name": "summary", "value": "={{ $json.output.summary }}"}, {"name": "cover_image", "value": "={{ $json.property_cover_image }}"}, {"name": "draft", "value": "false"}, {"name": "release_date", "value": "={{ new Date() }}"}, {"name": "reading_time", "value": "10"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-140, 600], "id": "f97a57ac-aabe-421c-bc4d-2d19c21d37f6", "name": "Add post to website posts page"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "=[You are a social media content formatter.]\n\nYou will receive an array of post objects. Each object includes the following fields:\n- name\n- author\n- property_cover_image\n- markdownContent\n- id\n- url\n\nYour Task:\n\nFor each post in the input array:\n\n1. Extract the following from `markdownContent`:\n   - All links → assign to a new field `links` (as an array of URLs).\n   - All image URLs → assign to a new field `images` (as an array of URLs).\n\n2. Create four versions of each post — one for each of the following platforms:\n   - LinkedIn\n   - Instagram\n   - Discord\n   - Facebook\n\n3. For each platform-specific version:\n   - Keep the following fields unchanged:\n     - name = {{ $json.name }}\n     - author = {{ $json.property_author }}\n     - markdownContent = {{ $json.markdownContent }}\n     - property_cover_image = {{ $json.property_cover_image }}\n     - notionId = {{ $json.id }}\n     - notionUrl = {{ $json.url }}\n   - Add the extracted `links` and `images` fields.\n   - Set the `platform` field to one of the four platforms.\n   - Add a new field `formattedPost` with content tailored to the platform.\n4.Create a new field formattedPost with light formatting of the original content based on the platform rules below.\n\nPlatform Formatting Rules:\n\nLinkedIn\n- Use the full original content.\n- Append 3–5 relevant hashtags inferred from the content.\n- Do not include links or images in the formattedPost.\n\nInstagram\n- Write a short, catchy caption (max 150 characters).\n- Include emojis.\n- Do not include any links.\n- Use the first image URL (if available) as `image_url`.\n- Format formattedPost as:\n  {\n    \"caption\": \"...\",\n    \"image_url\": \"...\"\n  }\n\nDiscord\n- Bold the title using **Title**.\n- Include 1–2 lines from the content.\n- Add the first link from links (if available) on a new line.\n- Add 1–2 emojis and a question to encourage engagement (e.g., \"💬 Thoughts?\").\n\nFacebook\n- Begin with the title.\n- Include the full content rewritten in a friendly tone.\n- Add one link from links at the end (if available).\n- Add a short call to action (e.g., \"Let us know what you think!\").\n\nReturn Format:\n\nOutput a JSON array of post objects, each containing:\n- name\n- platform (\"LinkedIn\", \"Instagram\", \"Discord\", or \"Facebook\")\n- author\n- markdownContent\n- property_cover_image\n- links\n- images\n- notionId\n- notionUrl\n- formattedPost (formatted according to platform rules)\n\nImportant:\n- Do not alter or rewrite the original markdownContent.\n- Do not include any explanations, metadata, or extra text.\n- Output only the correctly structured JSON array.\n\nReturn format example:\n{{ $input }}\n", "role": "system"}]}, "jsonOutput": true, "options": {}}, "id": "12a37c37-97be-4a8d-8e03-14d2e1f23230", "name": "Reformat Post Text", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [-460, 200], "typeVersion": 1.3, "credentials": {"openAiApi": {"id": "n6j811BvFHF0g3jI", "name": "OpenAi account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": false, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.platform}}", "rightValue": "=Facebook", "operator": {"type": "string", "operation": "equals"}, "id": "4471927b-b8b6-4a89-8f21-43fe1e51e21d"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Facebook Route"}, {"conditions": {"options": {"caseSensitive": false, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "********-0f35-477f-81ec-432347faa34a", "leftValue": "={{ $json.platform}}", "rightValue": "LinkedIn", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "LinkedIn Route"}, {"conditions": {"options": {"caseSensitive": false, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "e0446a59-2800-4d0e-9acd-40eb20780655", "leftValue": "={{ $json.platform}}", "rightValue": "Instagram", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Instagram Route"}, {"conditions": {"options": {"caseSensitive": false, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "3e2a15b0-eadb-4b63-a9d6-552f8fafc4d0", "leftValue": "={{ $json.platform}}", "rightValue": "Discord", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Discord Route"}]}, "options": {"ignoreCase": true, "allMatchingOutputs": true}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [960, 200], "id": "d79adb6c-97e0-49f8-be54-f088f798bb41", "name": "Switch"}, {"parameters": {"content": "\n## 🔄 After Publishing (Shared Workflow for All Platforms)\n\nOnce the post is published, continue with the following common steps:\n\n1. ✅ **Update Notion**  \n   Update the post status in Notion to `Done`.\n\n2. 📢 **Send Discord Notification**  \n   Notify the team in a Discord channel that a new post was published.\n\n3. 🌐 **Send to Company Website**  \n   Push the post data to the company website to display it publicly.\n\n4. ⏳ **Wait 24 Hours**  \n   Pause the workflow for 24 hours to later fetch or process engagement metrics.\n", "height": 481, "width": 1681, "color": 6}, "id": "e8b1cbc8-f0c9-4750-9a57-4cb9c259ce6a", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1280, -900], "typeVersion": 1}, {"parameters": {"fieldToSplitOut": "posts", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [580, 220], "id": "d8c92896-2063-435c-94a9-3c66907219a9", "name": "Split Out"}, {"parameters": {"workflowId": {"__rl": true, "value": "pQQA474v1CG3uE3C", "mode": "list", "cachedResultName": "Notion Extract Conent"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [-1260, 220], "id": "dbaea899-f53c-4c84-822d-c3420b36afb6", "name": "Execute Workflow"}, {"parameters": {"url": "={{ $json.property_cover_image }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1980, 40], "id": "bb002b1c-c766-48cf-b947-a77db7c3eacf", "name": "Download image for linkedIn"}, {"parameters": {"person": "k3ZTMMJ3Rg", "text": "={{ $json.links.length > 0 ? $json.formattedPost + \"\\n\" + $json.links.join(\"\\n\") : $json.formattedPost }}", "additionalFields": {}}, "id": "77d53dd1-b6ee-417b-951a-c577509a2c4c", "name": "Post on LinkedIn No image", "type": "n8n-nodes-base.linkedIn", "position": [2220, 260], "typeVersion": 1, "credentials": {"linkedInOAuth2Api": {"id": "AYVynT4oV7I7oLLy", "name": "LinkedIn account"}}}, {"parameters": {"person": "k3ZTMMJ3Rg", "text": "={{ $json.links.length > 0 ? $json.formattedPost + \"\\n\" + $json.links.join(\"\\n\") : $json.formattedPost }}", "shareMediaCategory": "IMAGE", "additionalFields": {}}, "id": "0f48930d-c0f6-47da-9cff-184f0fe840cd", "name": "Post on LinkedIn with images", "type": "n8n-nodes-base.linkedIn", "position": [2180, 40], "typeVersion": 1, "credentials": {"linkedInOAuth2Api": {"id": "AYVynT4oV7I7oLLy", "name": "LinkedIn account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "314b158b-f25e-4ff4-a4fe-39bc6b6bbac6", "leftValue": "={{ $json.property_cover_image }}", "rightValue": "=", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1460, 220], "id": "cb84e374-6b22-43d9-a3b1-7dbb09f92f48", "name": "If post have image or not"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [2500, 180], "id": "4846a039-083b-4676-b79c-e05111453d4b", "name": "<PERSON><PERSON>"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [2500, 400], "id": "76597df3-0729-46e0-a13e-bde943d672ba", "name": "Merge1"}, {"parameters": {"assignments": {"assignments": [{"id": "26afced0-9ba7-487c-a68a-6102c26f267e", "name": "posts", "value": "={{ $json.message.content.posts }}", "type": "array"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-60, 200], "id": "0a3d6444-0f17-44cf-a5b6-c7a848801846", "name": "Extract Text from Openi Ai"}, {"parameters": {"method": "POST", "url": "=https://graph.facebook.com/v20.0/{{ \"683603174832538\" }}/feed", "sendQuery": true, "specifyQuery": "json", "jsonQuery": "={\n  \"message\" : {{ $json.formattedPost.toJsonString() }},\n  \"access_token\" : \"EAAr5V5FvsnkBO6PrzVTCe5c3EIy2LSldoxFNJaXwQZCp97jvCaegAgTmdoGus7isZB6sch95wDwX5DAcCk45JTH2LFt47aOercUtU53GH53o5IZAOOCQR3VXHUOFN42gtRhxzHlePh1zgnTv6hGIMP9vTuIvzpIfRSf4vA9c4Rq9JCevpk2LWRpzl3yHUiedYEa\",\n  \"attached_media\": [{ \"media_fbid\":\"{{ $json.id }}\"}]\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2160, -240], "id": "88a6c7f5-2c22-4e5b-9ea4-ef56a1b0c834", "name": "Facebook post HTTP Request", "retryOnFail": true}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [2420, -140], "id": "d7eb4c2f-35c0-47ab-8b79-3f9665ca063d", "name": "Merge2"}, {"parameters": {"method": "POST", "url": "=https://graph.facebook.com/v20.0/{{\"683603174832538\"}}/photos", "sendQuery": true, "queryParameters": {"parameters": [{"name": "access_token", "value": "EAAr5V5FvsnkBO6PrzVTCe5c3EIy2LSldoxFNJaXwQZCp97jvCaegAgTmdoGus7isZB6sch95wDwX5DAcCk45JTH2LFt47aOercUtU53GH53o5IZAOOCQR3VXHUOFN42gtRhxzHlePh1zgnTv6hGIMP9vTuIvzpIfRSf4vA9c4Rq9JCevpk2LWRpzl3yHUiedYEa"}, {"name": "url", "value": "={{ $json.property_cover_image }}"}, {"name": "published", "value": "false"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1660, -60], "id": "0089360e-407f-4e1f-a664-2445b60065d0", "name": "Facebook post image HTTP Request1"}, {"parameters": {"method": "POST", "url": "=https://graph.facebook.com/v20.0/17841459370644146/media", "sendQuery": true, "queryParameters": {"parameters": [{"name": "access_token", "value": "EAAr5V5FvsnkBO4GItF26ExqPjK415InBPhv454ZBJ1wZAGcNGxwhr0MGmR2k0O92D304U93ydBoF3Uyebdad9r7Lz07qGZCG9tvZBiV6I31o7tBFoc76o6hFANrCTvBBlcEzaBHlJCsEtQeuEiE9qhnqYjrZA3PCvZBWbdCjS1RgeuDrmsaLSt0mZCOGZCC7Jw0V6VPF"}, {"name": "caption", "value": "={{ $json.formattedPost.caption }}"}, {"name": "image_url", "value": "={{ $json.property_cover_image }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1620, 520], "id": "20eefb1a-c388-4d8e-b361-3033e9de3c95", "name": "Instagram media creation HTTP Request"}, {"parameters": {"method": "POST", "url": "https://graph.facebook.com/v20.0/17841459370644146/media_publish", "sendQuery": true, "queryParameters": {"parameters": [{"name": "creation_id", "value": "={{ $json.id }}"}, {"name": "access_token", "value": "EAAr5V5FvsnkBO4GItF26ExqPjK415InBPhv454ZBJ1wZAGcNGxwhr0MGmR2k0O92D304U93ydBoF3Uyebdad9r7Lz07qGZCG9tvZBiV6I31o7tBFoc76o6hFANrCTvBBlcEzaBHlJCsEtQeuEiE9qhnqYjrZA3PCvZBWbdCjS1RgeuDrmsaLSt0mZCOGZCC7Jw0V6VPF"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1900, 520], "id": "52462e7f-ee1a-40a8-82c6-29156d4e7add", "name": "Instagram media upload HTTP Request"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [2180, 440], "id": "29c23ae5-2355-47b8-ab88-71896b80ddb3", "name": "Merge3"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [1960, -240], "id": "cc325e36-dc16-4570-af3b-b3593b585e1e", "name": "Merge4"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "314b158b-f25e-4ff4-a4fe-39bc6b6bbac6", "leftValue": "={{ $json.property_cover_image }}", "rightValue": "=", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1240, -240], "id": "63469b82-bd3a-4f33-930a-1bc181a8b128", "name": "If post have image or not1"}, {"parameters": {"method": "POST", "url": "=https://graph.facebook.com/v20.0/{{ \"683603174832538\" }}/feed", "sendQuery": true, "specifyQuery": "json", "jsonQuery": "={\n  \"message\" : {{ $json.formattedPost.toJsonString() }},\n  \"access_token\" : \"EAAr5V5FvsnkBO6PrzVTCe5c3EIy2LSldoxFNJaXwQZCp97jvCaegAgTmdoGus7isZB6sch95wDwX5DAcCk45JTH2LFt47aOercUtU53GH53o5IZAOOCQR3VXHUOFN42gtRhxzHlePh1zgnTv6hGIMP9vTuIvzpIfRSf4vA9c4Rq9JCevpk2LWRpzl3yHUiedYEa\"\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1660, -420], "id": "1a950d9b-c25b-475c-9ec8-25e06faa5aa7", "name": "Facebook post HTTP Request1", "retryOnFail": true}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [1920, -400], "id": "24c391c8-eb66-491f-aa2d-6e3898f24bd9", "name": "Merge5"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "314b158b-f25e-4ff4-a4fe-39bc6b6bbac6", "leftValue": "={{ $json.property_cover_image }}", "rightValue": "=", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1420, 500], "id": "8718b30c-9157-4759-b817-eb2092f0a0cb", "name": "If post have image or not2"}, {"parameters": {"workflowId": {"__rl": true, "value": "2HqANHclXFYV7BFl", "mode": "list", "cachedResultName": "Update notion status and posted workflow"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [3120, 180], "id": "9a702b26-d315-4502-8bab-01e4710cebd7", "name": "Set post status to \"Published\" Workflow"}, {"parameters": {"promptType": "define", "text": "=message: {{ $json.markdownContent }}", "options": {"systemMessage": "You are a content classification assistant for a blogging platform.\n\nGiven the following message, analyze its content and return:\n\nA one line summary of the entire topic\n\nA single category that best describes the overall topic.\n\nA list of 1 to 5 tags that highlight key themes, topics, or keywords from the message.\n\nYour response must strictly follow this JSON format (no additional explanation, no markdown):\n\njson\n{\n  \"summary\": \"summary of the message\"\n  \"category\": \"decided category\",\n  \"tags\": \"[tag1, tag2, tag3]\"\n}\n\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-980, 640], "id": "f2ec46b6-b358-4f8c-af68-76413d6a69ee", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-960, 800], "id": "f0073d70-c9a6-45d3-8897-c28c5061165e", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "n6j811BvFHF0g3jI", "name": "OpenAi account"}}}, {"parameters": {"jsCode": "for (const item of $input.all()) {\n  const parsedOutput = JSON.parse(item.json.output); // Parse the output string to JSON\n  parsedOutput.myNewField = 1; // Add your new field\n  item.json.output = parsedOutput; // Replace the string with the updated object\n}\n\nreturn $input.all();"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-600, 640], "id": "a311c59e-c9f1-46a1-88cc-49fa76c74cc5", "name": "Code1"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [-380, 600], "id": "125fe27a-08d6-47e2-b796-cd79a2240678", "name": "Merge6"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [120, 560], "id": "2d2cc219-9ada-4f7a-9c3c-a4a6d02eefce", "name": "Merge7"}, {"parameters": {"assignments": {"assignments": [{"id": "c191b526-1b75-4724-af38-2a9f1ea4a60d", "name": "platform", "value": "website", "type": "string"}, {"id": "7ab827fc-612d-4c82-90fb-a719d1199261", "name": "notionUrl", "value": "={{ $json.url }}", "type": "string"}]}, "includeOtherFields": true, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [340, 560], "id": "ab2f3d52-bb52-412e-8c1a-bc2bcd898505", "name": "<PERSON>"}, {"parameters": {"content": "# Important Notes\n## Take the link of blog post and add it to the linkedin post\n### make the url of the blog shorten then added it to linkedin\nExample:\nLearn more at (Link)", "height": 241, "width": 721, "color": 5}, "id": "25a6b0ad-aa85-4820-a854-3fbc6b54102a", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-2280, 500], "typeVersion": 1}], "pinData": {}, "connections": {"Schedule Trigger": {"main": [[{"node": "Execute Workflow", "type": "main", "index": 0}]]}, "Discord Announcement": {"main": [[]]}, "Add post to website posts page": {"main": [[{"node": "Merge7", "type": "main", "index": 1}]]}, "Reformat Post Text": {"main": [[{"node": "Extract Text from Openi Ai", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "If post have image or not1", "type": "main", "index": 0}], [], [{"node": "If post have image or not2", "type": "main", "index": 0}], [{"node": "Discord Announcement", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Execute Workflow": {"main": [[{"node": "Reformat Post Text", "type": "main", "index": 0}, {"node": "AI Agent", "type": "main", "index": 0}, {"node": "Merge6", "type": "main", "index": 0}, {"node": "Merge7", "type": "main", "index": 0}]]}, "Post on LinkedIn No image": {"main": [[{"node": "Merge1", "type": "main", "index": 0}]]}, "Download image for linkedIn": {"main": [[{"node": "Post on LinkedIn with images", "type": "main", "index": 0}]]}, "If post have image or not": {"main": [[{"node": "Download image for linkedIn", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 1}], [{"node": "Post on LinkedIn No image", "type": "main", "index": 0}, {"node": "Merge1", "type": "main", "index": 1}]]}, "Post on LinkedIn with images": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "Set post status to \"Published\" Workflow", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "Set post status to \"Published\" Workflow", "type": "main", "index": 0}]]}, "Extract Text from Openi Ai": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Facebook post HTTP Request": {"main": [[{"node": "Merge2", "type": "main", "index": 0}]]}, "Merge2": {"main": [[{"node": "Set post status to \"Published\" Workflow", "type": "main", "index": 0}]]}, "Facebook post image HTTP Request1": {"main": [[{"node": "Merge4", "type": "main", "index": 0}]]}, "Instagram media creation HTTP Request": {"main": [[{"node": "Instagram media upload HTTP Request", "type": "main", "index": 0}]]}, "Instagram media upload HTTP Request": {"main": [[{"node": "Merge3", "type": "main", "index": 0}]]}, "Merge3": {"main": [[{"node": "Set post status to \"Published\" Workflow", "type": "main", "index": 0}]]}, "Merge4": {"main": [[{"node": "Facebook post HTTP Request", "type": "main", "index": 0}]]}, "If post have image or not1": {"main": [[{"node": "Facebook post image HTTP Request1", "type": "main", "index": 0}, {"node": "Merge4", "type": "main", "index": 1}, {"node": "Merge2", "type": "main", "index": 1}], [{"node": "Facebook post HTTP Request1", "type": "main", "index": 0}, {"node": "Merge5", "type": "main", "index": 1}]]}, "Facebook post HTTP Request1": {"main": [[{"node": "Merge5", "type": "main", "index": 0}]]}, "Merge5": {"main": [[{"node": "Set post status to \"Published\" Workflow", "type": "main", "index": 0}]]}, "If post have image or not2": {"main": [[{"node": "Instagram media creation HTTP Request", "type": "main", "index": 0}, {"node": "Merge3", "type": "main", "index": 1}]]}, "AI Agent": {"main": [[{"node": "Code1", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Code1": {"main": [[{"node": "Merge6", "type": "main", "index": 1}]]}, "Merge6": {"main": [[{"node": "Add post to website posts page", "type": "main", "index": 0}]]}, "Merge7": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Set post status to \"Published\" Workflow", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "QjfQ9CWNToFgyvDh"}, "versionId": "8221b6cf-e86f-4efe-b3af-0fea797b2077", "meta": {"templateCredsSetupCompleted": true, "instanceId": "36e066b6e7fd7141a4037cea4720adcd14b55b97aa9443f1eac8dd561c769073"}, "id": "TGrzOrGJrmgBM5uu", "tags": []}