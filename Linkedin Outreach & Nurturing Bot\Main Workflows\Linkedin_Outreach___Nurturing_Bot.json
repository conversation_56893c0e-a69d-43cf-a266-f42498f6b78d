{"name": "Linkedin Outreach & Nurturing Bot", "nodes": [{"parameters": {"rule": {"interval": [{}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-120, 80], "id": "65e9128a-8362-43c3-b2e3-0f86c5a39aab", "name": "Schedule Trigger2"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [540, 260], "id": "fdb311aa-0267-46fc-a6f0-732386961047", "name": "Loop Over Items"}, {"parameters": {"assignments": {"assignments": [{"id": "3e145973-2c8f-44ad-8765-09cd85f18655", "name": "output", "value": "={{JSON.parse( $json.resultObject) }}", "type": "array"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1200, 60], "id": "06613d28-4994-4d7f-bd49-4d5277ef4b49", "name": "parse string to json"}, {"parameters": {"content": "# Track user replies and update <PERSON><PERSON> in one of these\n## - Status = Connected / Replied / Ignored\n## - Notes = Last message content or user reply\n## - Engagement = Positive / Neutral / Unread\n ", "height": 640, "width": 1240}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2080, -340], "id": "44bcd3bc-3b54-4d36-91fe-4b606bb01a48", "name": "<PERSON><PERSON>"}, {"parameters": {"resource": "databasePage", "operation": "update", "pageId": {"__rl": true, "mode": "url", "value": ""}, "options": {}}, "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [3120, 0], "id": "8ea68aff-7bdf-4e20-9bac-a19fa07e61cd", "name": "Track Replies in Notion CRM", "credentials": {"notionApi": {"id": "5njFc78PPtVBGWk3", "name": "Notion account"}}}, {"parameters": {"url": "https://api.phantombuster.com/api/v2/containers/fetch-result-object", "sendQuery": true, "queryParameters": {"parameters": [{"name": "id", "value": "={{ $json.containerId }}"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Phantombuster-Key", "value": "uQjtfmdZVL1QOhG5vBnTAAIHlugdok2OcfkzKhxUgIY"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [980, 60], "id": "a29d1bcf-e55c-45d2-b321-fee2d84757f6", "name": "Phantombuster - Get Profiles Info"}, {"parameters": {"method": "POST", "url": "https://api.phantombuster.com/api/v2/agents/launch", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Phantombuster-Key", "value": "uQjtfmdZVL1QOhG5vBnTAAIHlugdok2OcfkzKhxUgIY"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"id\": \"****************\",\n          \"arguments\": {\n            \"pushResultToCRM\": false,\n            \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36\",\n            \"numberOfAddsPerLaunch\": 200,\n            \"identityId\": \"7637455043137822\",\n            \"sessionCookie\": \"AQEDAS8Q098F0dGbAAABkbReQzkAAAGXNlIobk0AyZgFu2Tp9_-b_9yeHaqP5lFts78Kk2DD5DNob4eDQovD6Y__5xNv1bViUAx0_KfehAhk5MazK4GX66Ue7bAKCLOgagBrV2L7-JilqcjXqLUoHMNY\",\n            \"spreadsheetUrl\": \n              \"{{ $json.property_linked_in_url }}\",\n            \"crmProfileUrlColumnName\": \"ProfileUrl\"\n            },\n          \"saveArguments\": false\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1200, 360], "id": "4c947cca-2cee-4425-9c74-ba8d2b784058", "name": "Phantombuster - Launch LinkedIn Profile Scraper phantom"}, {"parameters": {"amount": 25}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [920, 320], "id": "89bbc30b-b6b8-4b26-96fc-218a80f4287f", "name": "Wait for Phantombuster rate limit", "webhookId": "effbaad6-d520-4b35-9298-88494c86ad34"}, {"parameters": {"amount": 25}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [760, 60], "id": "d1c157ba-7856-4895-930a-3dc3fceebc7f", "name": "Wait for Phantombuster rate limit 2", "webhookId": "81c48df6-e61f-4114-a0f1-8afedc5d2179"}, {"parameters": {"resource": "databasePage", "operation": "getAll", "databaseId": {"__rl": true, "value": "202d00a6-1f99-8033-bb68-e6d552eeb549", "mode": "list", "cachedResultName": "Professional Contacts", "cachedResultUrl": "https://www.notion.so/202d00a61f998033bb68e6d552eeb549"}, "returnAll": true, "filterType": "manual", "filters": {"conditions": [{"key": "LinkedIn URL|url", "condition": "is_not_empty"}]}, "options": {}}, "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [140, -100], "id": "ec55f83b-ae8e-40e4-a968-f72d7517e34d", "name": "Notion - Linkedin Contacts", "credentials": {"notionApi": {"id": "5njFc78PPtVBGWk3", "name": "Notion account"}}}, {"parameters": {"method": "POST", "url": "https://api.phantombuster.com/api/v2/agents/launch", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Phantombuster-Key", "value": "uQjtfmdZVL1QOhG5vBnTAAIHlugdok2OcfkzKhxUgIY"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"id\": \"****************\",\n  \"arguments\": {\n    \"maxNumberOfConnectionsPerDay\": 20,\n    \"requestsTime\": \"Weekdays during working hours\",\n    \"sessionCookie\": \"AQEDAS8Q098F0dGbAAABkbReQzkAAAGXNlIobk0AyZgFu2Tp9_-b_9yeHaqP5lFts78Kk2DD5DNob4eDQovD6Y__5xNv1bViUAx0_KfehAhk5MazK4GX66Ue7bAKCLOgagBrV2L7-JilqcjXqLUoHMNY\",\n    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36\",\n    \"customizeInvite\": true,\n    \"invitationMessage\": \"hi {{ $json.output[0].firstName }} lets connect\",\n    \"firstFollowUp\": true,\n    \"firstFollowUpTime\": \"0 days\",\n    \"followUpMessage\": \"Hi {{ $json.output[0].firstName }}, I loved your insights on {{ $json.output[0].companyName }}. Would love to connect and exchange notes on smart operations!\\n\",\n    \"secondFollowUp\": true,\n    \"secondFollowUpTime\": \"1 day\",\n    \"secondFollowUpMessage\": \"Hey {{ $json.output[0].firstName }}, wanted to share a quick guide we created on automation ROI for startups—happy to hear your thoughts.\",\n    \"leadsSourceUrl\": \"{{ $json.output[0].linkedinProfileUrl }}\",\n    \"crmProfileUrlColumnName\": \"ProfileUrl\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1880, 220], "id": "867bbfb0-de49-4763-ada1-e5728732f92b", "name": "Phantombuster - LinkedIn Auto-send Connection Request"}, {"parameters": {"url": "https://api.phantombuster.com/api/v2/containers/fetch-result-object", "sendQuery": true, "queryParameters": {"parameters": [{"name": "id", "value": "={{ $json.containerId }}"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Phantombuster-Key", "value": "uQjtfmdZVL1QOhG5vBnTAAIHlugdok2OcfkzKhxUgIY"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2200, 40], "id": "76ba0c12-2610-49cb-a4ec-acd28976032b", "name": "Phantombuster - Get Status after sending connection"}, {"parameters": {"content": "# Extract Linkedin users from Notion\n", "height": 220, "width": 360, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [20, -360], "id": "2773d3f6-267d-43b1-9a71-328636345890", "name": "Sticky Note1"}, {"parameters": {"assignments": {"assignments": [{"id": "3e145973-2c8f-44ad-8765-09cd85f18655", "name": "output", "value": "={{JSON.parse( $json.resultObject) }}", "type": "array"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2440, 40], "id": "21572976-af2a-4fbe-ada6-0c3d7b335688", "name": "parse string to json 2"}, {"parameters": {"mode": "combine", "advanced": true, "mergeByFields": {"values": [{"field1": "={{ $json.linkedInUrl }}", "field2": "={{ $json.output[0].linkedinProfileUrl }}"}]}, "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [2700, -100], "id": "de087a49-5002-4d44-b677-9368c37830d3", "name": "<PERSON><PERSON>"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [1460, 60], "id": "5af1e858-046a-42eb-bf9a-d9571cd5d0a8", "name": "Loop Over Items 2"}, {"parameters": {"amount": 25}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1660, 200], "id": "82f847bb-e690-4154-a964-ba865021ca49", "name": "Wait for Phantombuster rate limit1", "webhookId": "9aec8224-0a7c-41ea-8bfc-be115b0c9fd2"}, {"parameters": {"amount": 60}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1940, 40], "id": "bb20e8ad-e1bb-471c-85f2-617caaaccbb4", "name": "Wait 3 Days", "webhookId": "161e65e5-572a-4e51-a7df-ffad95c69e58"}, {"parameters": {"assignments": {"assignments": [{"id": "5cf80878-c377-4789-8e18-b6c51f28199b", "name": "notionUrl", "value": "={{ $json.url }}", "type": "string"}, {"id": "5e93be54-0c7b-4629-8280-4b184b45dcf2", "name": "linkedInUrl", "value": "={{ $json.property_linked_in_url }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1040, -200], "id": "e02ae1f3-26f2-4346-bf9e-bae7fe2158fa", "name": "<PERSON>"}, {"parameters": {"content": "# Loop through linedin urls\n## - send linkedin urls to phantombuster to get their personal information", "height": 540, "width": 1020, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [380, -40], "id": "36946ed2-3263-4bc3-af18-4f3e45c2fc6a", "name": "Sticky Note2"}, {"parameters": {"content": "## Sending linkedin user a connection request and messages\n### - Send user connection request with a message\n### - Send a follow up message \n### - Send a second follow up message\n \n### Wait 3 days then get the reponse", "height": 540, "width": 620, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1440, -140], "id": "1482b486-6026-48fb-a21c-acf0be0b3f28", "name": "Sticky Note3"}, {"parameters": {"content": "# Ammar el sayed ToDo List\n## 1-Merge the retrieved data from notion we need notion url to update notion db with data comming from node parse string to json 2\n## 2- update notion with these data\n### - Status = Connected / Replied / Ignored\n### - Notes = Last message content or user reply\n### - Engagement = Positive / Neutral / Unread\n ", "height": 360, "width": 760, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1240, -620], "id": "bca3a8af-1645-477b-ae49-00e1fecdbf0c", "name": "Sticky Note4"}], "pinData": {}, "connections": {"Schedule Trigger2": {"main": [[{"node": "Notion - Linkedin Contacts", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Wait for Phantombuster rate limit 2", "type": "main", "index": 0}], [{"node": "Wait for Phantombuster rate limit", "type": "main", "index": 0}]]}, "parse string to json": {"main": [[{"node": "Loop Over Items 2", "type": "main", "index": 0}]]}, "Send Connection Request and follow up message": {"main": [[{"node": "Wait 3 Days", "type": "main", "index": 0}]]}, "Get Profiles Info2": {"main": [[{"node": "Code1", "type": "main", "index": 0}]]}, "Phantombuster - Get Profiles Info": {"main": [[{"node": "parse string to json", "type": "main", "index": 0}]]}, "Phantombuster - Launch LinkedIn Profile Scraper phantom": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Wait for Phantombuster rate limit": {"main": [[{"node": "Phantombuster - Launch LinkedIn Profile Scraper phantom", "type": "main", "index": 0}]]}, "Wait for Phantombuster rate limit 2": {"main": [[{"node": "Phantombuster - Get Profiles Info", "type": "main", "index": 0}]]}, "Notion - Linkedin Contacts": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}, {"node": "<PERSON>", "type": "main", "index": 0}]]}, "Phantombuster - LinkedIn Auto-send Connection Request": {"main": [[{"node": "Loop Over Items 2", "type": "main", "index": 0}]]}, "Phantombuster - Get Status after sending connection": {"main": [[{"node": "parse string to json 2", "type": "main", "index": 0}]]}, "parse string to json 2": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Merge": {"main": [[{"node": "Track Replies in Notion CRM", "type": "main", "index": 0}]]}, "Loop Over Items 2": {"main": [[{"node": "Wait 3 Days", "type": "main", "index": 0}], [{"node": "Wait for Phantombuster rate limit1", "type": "main", "index": 0}]]}, "Wait for Phantombuster rate limit1": {"main": [[{"node": "Phantombuster - LinkedIn Auto-send Connection Request", "type": "main", "index": 0}]]}, "Wait 3 Days": {"main": [[{"node": "Phantombuster - Get Status after sending connection", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "f31f8b32-bdd0-4821-bbd3-a182b8bf1bdc", "meta": {"templateCredsSetupCompleted": true, "instanceId": "36e066b6e7fd7141a4037cea4720adcd14b55b97aa9443f1eac8dd561c769073"}, "id": "lf5pPv1IrQ6a0pMX", "tags": []}